/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fapp%2Fglobals.css&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fcomponents%2Fui%2FCustomCursor.tsx&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fcomponents%2Fui%2FSmoothScroll.tsx&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fapp%2Fglobals.css&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fcomponents%2Fui%2FCustomCursor.tsx&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fcomponents%2Fui%2FSmoothScroll.tsx&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ThemeProvider.tsx */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/CustomCursor.tsx */ \"(app-pages-browser)/./src/components/ui/CustomCursor.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/SmoothScroll.tsx */ \"(app-pages-browser)/./src/components/ui/SmoothScroll.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fapp%2Fglobals.css&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fcomponents%2Fui%2FCustomCursor.tsx&modules=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fwebdesav%2Fdevoradev%2Fsrc%2Fcomponents%2Fui%2FSmoothScroll.tsx&server=false!\n"));

/***/ })

});