"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/Hero.tsx":
/*!******************************************!*\
  !*** ./src/components/sections/Hero.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: function() { return /* binding */ Hero; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _components_ui_AnimatedText__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/AnimatedText */ \"(app-pages-browser)/./src/components/ui/AnimatedText.tsx\");\n/* __next_internal_client_entry_do_not_use__ Hero auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Hero() {\n    _s();\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const backgroundRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__.ScrollTrigger);\n            // Hero text animation\n            const tl = gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.timeline();\n            tl.fromTo(\".hero-title\", {\n                y: 100,\n                opacity: 0\n            }, {\n                y: 0,\n                opacity: 1,\n                duration: 1.2,\n                ease: \"power3.out\"\n            }).fromTo(\".hero-subtitle\", {\n                y: 50,\n                opacity: 0\n            }, {\n                y: 0,\n                opacity: 1,\n                duration: 1,\n                ease: \"power3.out\"\n            }, \"-=0.8\").fromTo(\".hero-description\", {\n                y: 30,\n                opacity: 0\n            }, {\n                y: 0,\n                opacity: 1,\n                duration: 0.8,\n                ease: \"power3.out\"\n            }, \"-=0.6\").fromTo(\".hero-cta\", {\n                y: 20,\n                opacity: 0\n            }, {\n                y: 0,\n                opacity: 1,\n                duration: 0.6,\n                ease: \"power3.out\"\n            }, \"-=0.4\");\n            // Parallax background\n            gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(backgroundRef.current, {\n                yPercent: -50,\n                ease: \"none\",\n                scrollTrigger: {\n                    trigger: heroRef.current,\n                    start: \"top bottom\",\n                    end: \"bottom top\",\n                    scrub: true\n                }\n            });\n            // Floating animation for decorative elements\n            gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.to(\".floating-element\", {\n                y: -20,\n                duration: 2,\n                ease: \"power2.inOut\",\n                yoyo: true,\n                repeat: -1,\n                stagger: 0.3\n            });\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: heroRef,\n        id: \"home\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: backgroundRef,\n                className: \"absolute inset-0 parallax-bg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-primary-500/10 to-purple-500/10 dark:from-primary-400/5 dark:to-purple-400/5\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"floating-element absolute top-20 left-10 w-20 h-20 bg-primary-500/20 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"floating-element absolute top-40 right-20 w-32 h-32 bg-purple-500/20 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"floating-element absolute bottom-40 left-20 w-24 h-24 bg-blue-500/20 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"floating-element absolute bottom-20 right-10 w-16 h-16 bg-pink-500/20 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: textRef,\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hero-title text-5xl md:text-7xl lg:text-8xl font-bold leading-tight\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_AnimatedText__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                    text: \"თანამედროვე\",\n                                    className: \"text-gradient block\",\n                                    delay: 0.2,\n                                    stagger: 0.08\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_AnimatedText__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                    text: \"ვებ სტუდია\",\n                                    className: \"text-gray-900 dark:text-white block\",\n                                    delay: 0.8,\n                                    stagger: 0.08\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hero-subtitle text-xl md:text-2xl lg:text-3xl font-medium text-gray-700 dark:text-gray-300 max-w-4xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_AnimatedText__WEBPACK_IMPORTED_MODULE_2__.TypewriterText, {\n                                text: \"ჩვენ ვქმნით დაუვიწყარ ციფრულ გამოცდილებებს\",\n                                delay: 1.5,\n                                speed: 80\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                            className: \"hero-description text-lg md:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            children: \"SEO ოპტიმიზაციიდან UI/UX დიზაინამდე, ვებ აპლიკაციებიდან მობილურ აპებამდე - ჩვენ გთავაზობთ სრულ სპექტრს ციფრული სერვისებისა, რომლებიც თქვენს ბიზნესს წარმატებისკენ მიგიყვანთ.\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"hero-cta flex flex-col sm:flex-row gap-4 justify-center items-center pt-8\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"px-8 py-4 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 text-lg\",\n                                    children: \"პროექტის დაწყება\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"px-8 py-4 bg-transparent border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:border-primary-500 hover:text-primary-500 font-semibold rounded-full transition-all duration-300 text-lg\",\n                                    children: \"ჩვენი სამუშაოები\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                            animate: {\n                                y: [\n                                    0,\n                                    10,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10\"\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Hero.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(Hero, \"Vm3qc0DHDrwDrmQxHmPk/aLu4uo=\");\n_c = Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Hero.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/AnimatedText.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/AnimatedText.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedText: function() { return /* binding */ AnimatedText; },\n/* harmony export */   FadeInText: function() { return /* binding */ FadeInText; },\n/* harmony export */   TypewriterText: function() { return /* binding */ TypewriterText; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ AnimatedText,TypewriterText,FadeInText auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AnimatedText(param) {\n    let { text, className = \"\", delay = 0, stagger = 0.05 } = param;\n    _s();\n    const textRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            var _textRef_current;\n            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\n            const chars = (_textRef_current = textRef.current) === null || _textRef_current === void 0 ? void 0 : _textRef_current.querySelectorAll(\".char\");\n            if (chars) {\n                gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(chars, {\n                    y: 100,\n                    opacity: 0,\n                    rotationX: -90\n                }, {\n                    y: 0,\n                    opacity: 1,\n                    rotationX: 0,\n                    duration: 0.8,\n                    stagger: stagger,\n                    delay: delay,\n                    ease: \"back.out(1.7)\",\n                    scrollTrigger: {\n                        trigger: textRef.current,\n                        start: \"top 80%\",\n                        toggleActions: \"play none none reverse\"\n                    }\n                });\n            }\n        }\n    }, [\n        delay,\n        stagger\n    ]);\n    const splitText = text.split(\"\").map((char, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"char inline-block\",\n            style: {\n                transformOrigin: \"50% 50% -50px\"\n            },\n            children: char === \" \" ? \"\\xa0\" : char\n        }, index, false, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/AnimatedText.tsx\",\n            lineNumber: 51,\n            columnNumber: 5\n        }, this));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: textRef,\n        className: className,\n        children: splitText\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/AnimatedText.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n_s(AnimatedText, \"03a/W9zemG2g1Vmhz2i1tulxw6U=\");\n_c = AnimatedText;\nfunction TypewriterText(param) {\n    let { text, className = \"\", speed = 50, delay = 0 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: className,\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        transition: {\n            delay\n        },\n        children: text.split(\"\").map((char, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: delay + index * speed / 1000,\n                    duration: 0.1\n                },\n                children: char\n            }, index, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/AnimatedText.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/AnimatedText.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_c1 = TypewriterText;\nfunction FadeInText(param) {\n    let { children, className = \"\", direction = \"up\", delay = 0, duration = 0.6 } = param;\n    const directionVariants = {\n        up: {\n            y: 50,\n            opacity: 0\n        },\n        down: {\n            y: -50,\n            opacity: 0\n        },\n        left: {\n            x: 50,\n            opacity: 0\n        },\n        right: {\n            x: -50,\n            opacity: 0\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: directionVariants[direction],\n        whileInView: {\n            x: 0,\n            y: 0,\n            opacity: 1\n        },\n        transition: {\n            duration,\n            delay\n        },\n        viewport: {\n            once: true\n        },\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/AnimatedText.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_c2 = FadeInText;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AnimatedText\");\n$RefreshReg$(_c1, \"TypewriterText\");\n$RefreshReg$(_c2, \"FadeInText\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/AnimatedText.tsx\n"));

/***/ })

});