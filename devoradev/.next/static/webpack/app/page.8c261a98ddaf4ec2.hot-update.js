"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/Hero */ \"(app-pages-browser)/./src/components/sections/Hero.tsx\");\n/* harmony import */ var _components_sections_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/About */ \"(app-pages-browser)/./src/components/sections/About.tsx\");\n/* harmony import */ var _components_sections_Services__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/Services */ \"(app-pages-browser)/./src/components/sections/Services.tsx\");\n/* harmony import */ var _components_sections_Portfolio__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/Portfolio */ \"(app-pages-browser)/./src/components/sections/Portfolio.tsx\");\n/* harmony import */ var _components_sections_Testimonials__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sections/Testimonials */ \"(app-pages-browser)/./src/components/sections/Testimonials.tsx\");\n/* harmony import */ var _components_sections_FAQ__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/sections/FAQ */ \"(app-pages-browser)/./src/components/sections/FAQ.tsx\");\n/* harmony import */ var _components_sections_CTA__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/sections/CTA */ \"(app-pages-browser)/./src/components/sections/CTA.tsx\");\n/* harmony import */ var _components_sections_Contact__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/sections/Contact */ \"(app-pages-browser)/./src/components/sections/Contact.tsx\");\n/* harmony import */ var _components_layout_Navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/layout/Navigation */ \"(app-pages-browser)/./src/components/layout/Navigation.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Register GSAP plugins\nif (true) {\n    gsap__WEBPACK_IMPORTED_MODULE_12__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_13__.ScrollTrigger);\n}\nfunction Home() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize GSAP animations\n        gsap__WEBPACK_IMPORTED_MODULE_12__.gsap.fromTo(\".fade-in-up\", {\n            y: 100,\n            opacity: 0\n        }, {\n            y: 0,\n            opacity: 1,\n            duration: 1,\n            stagger: 0.2,\n            scrollTrigger: {\n                trigger: \".fade-in-up\",\n                start: \"top 80%\",\n                end: \"bottom 20%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n        // Parallax effect for hero section\n        gsap__WEBPACK_IMPORTED_MODULE_12__.gsap.to(\".parallax-bg\", {\n            yPercent: -50,\n            ease: \"none\",\n            scrollTrigger: {\n                trigger: \".parallax-bg\",\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: true\n            }\n        });\n        // Text reveal animation\n        gsap__WEBPACK_IMPORTED_MODULE_12__.gsap.fromTo(\".text-reveal\", {\n            y: 100,\n            opacity: 0\n        }, {\n            y: 0,\n            opacity: 1,\n            duration: 1.2,\n            ease: \"power3.out\",\n            scrollTrigger: {\n                trigger: \".text-reveal\",\n                start: \"top 85%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n        return ()=>{\n            gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_13__.ScrollTrigger.getAll().forEach((trigger)=>trigger.kill());\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navigation__WEBPACK_IMPORTED_MODULE_10__.Navigation, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__.Hero, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_About__WEBPACK_IMPORTED_MODULE_3__.About, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Services__WEBPACK_IMPORTED_MODULE_4__.Services, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Portfolio__WEBPACK_IMPORTED_MODULE_5__.Portfolio, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Testimonials__WEBPACK_IMPORTED_MODULE_6__.Testimonials, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_FAQ__WEBPACK_IMPORTED_MODULE_7__.FAQ, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_CTA__WEBPACK_IMPORTED_MODULE_8__.CTA, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Contact__WEBPACK_IMPORTED_MODULE_9__.Contact, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_11__.Footer, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/CTA.tsx":
/*!*****************************************!*\
  !*** ./src/components/sections/CTA.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CTA: function() { return /* binding */ CTA; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_InteractiveCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/InteractiveCard */ \"(app-pages-browser)/./src/components/ui/InteractiveCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ CTA auto */ \n\n\nfunction CTA() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 lg:py-32 bg-gradient-to-br from-primary-600 via-purple-600 to-pink-600 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-48 h-48 bg-white/5 rounded-full blur-2xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 right-10 w-36 h-36 bg-white/5 rounded-full blur-2xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InteractiveCard__WEBPACK_IMPORTED_MODULE_1__.FloatingElement, {\n                className: \"absolute top-20 left-20\",\n                amplitude: 15,\n                duration: 4,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-white/20 rounded-lg rotate-45\"\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InteractiveCard__WEBPACK_IMPORTED_MODULE_1__.FloatingElement, {\n                className: \"absolute top-32 right-32\",\n                amplitude: 20,\n                duration: 3,\n                delay: 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-6 h-6 bg-white/30 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InteractiveCard__WEBPACK_IMPORTED_MODULE_1__.FloatingElement, {\n                className: \"absolute bottom-32 left-32\",\n                amplitude: 18,\n                duration: 5,\n                delay: 2,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 h-10 bg-white/15 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl lg:text-6xl font-bold text-white mb-6\",\n                            children: [\n                                \"მზად ხართ თქვენი\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                                    children: \"ციფრული\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                \"მომავლის შესაქმნელად?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-xl lg:text-2xl text-white/90 mb-8 leading-relaxed\",\n                            children: \"ჩვენ ვქმნით ვებსაიტებს, აპლიკაციებს და ციფრულ გამოცდილებებს, რომლებიც თქვენს ბიზნესს ახალ დონეზე აიყვანს\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"grid md:grid-cols-3 gap-6 mb-12\",\n                            children: [\n                                {\n                                    icon: \"⚡\",\n                                    text: \"სწრაფი განვითარება\"\n                                },\n                                {\n                                    icon: \"\\uD83C\\uDFAF\",\n                                    text: \"ინდივიდუალური მიდგომა\"\n                                },\n                                {\n                                    icon: \"\\uD83D\\uDE80\",\n                                    text: \"გარანტირებული შედეგი\"\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.6 + index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"flex items-center justify-center space-x-3 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: feature.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: feature.text\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"px-8 py-4 bg-white text-primary-600 font-bold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 text-lg\",\n                                    children: \"უფასო კონსულტაცია\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05,\n                                        y: -2\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"px-8 py-4 bg-transparent border-2 border-white text-white font-bold rounded-full hover:bg-white hover:text-primary-600 transition-all duration-300 text-lg\",\n                                    children: \"ჩვენი სამუშაოები\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            whileInView: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"mt-12 text-white/80\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-4\",\n                                    children: \"ან დაგვიკავშირდით პირდაპირ:\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            className: \"flex items-center space-x-2 hover:text-white transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDCE7\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:+995555123456\",\n                                            className: \"flex items-center space-x-2 hover:text-white transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDCF1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+995 555 123 456\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-grid-pattern opacity-10\"\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/CTA.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = CTA;\nvar _c;\n$RefreshReg$(_c, \"CTA\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/CTA.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/InteractiveCard.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/InteractiveCard.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingElement: function() { return /* binding */ FloatingElement; },\n/* harmony export */   InteractiveCard: function() { return /* binding */ InteractiveCard; },\n/* harmony export */   MagneticButton: function() { return /* binding */ MagneticButton; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ InteractiveCard,MagneticButton,FloatingElement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction InteractiveCard(param) {\n    let { children, className = \"\", glowColor = \"rgba(14, 165, 233, 0.3)\", tiltIntensity = 10 } = param;\n    _s();\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const x = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useMotionValue)(0);\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useMotionValue)(0);\n    const mouseXSpring = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useSpring)(x);\n    const mouseYSpring = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useSpring)(y);\n    const rotateX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(mouseYSpring, [\n        -0.5,\n        0.5\n    ], [\n        tiltIntensity,\n        -tiltIntensity\n    ]);\n    const rotateY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(mouseXSpring, [\n        -0.5,\n        0.5\n    ], [\n        -tiltIntensity,\n        tiltIntensity\n    ]);\n    const handleMouseMove = (e)=>{\n        if (!ref.current) return;\n        const rect = ref.current.getBoundingClientRect();\n        const width = rect.width;\n        const height = rect.height;\n        const mouseX = e.clientX - rect.left;\n        const mouseY = e.clientY - rect.top;\n        const xPct = mouseX / width - 0.5;\n        const yPct = mouseY / height - 0.5;\n        x.set(xPct);\n        y.set(yPct);\n    };\n    const handleMouseLeave = ()=>{\n        setIsHovered(false);\n        x.set(0);\n        y.set(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        ref: ref,\n        onMouseMove: handleMouseMove,\n        onMouseEnter: ()=>setIsHovered(true),\n        onMouseLeave: handleMouseLeave,\n        style: {\n            rotateX,\n            rotateY,\n            transformStyle: \"preserve-3d\"\n        },\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"absolute -inset-1 rounded-2xl opacity-0 blur-xl\",\n                style: {\n                    background: glowColor\n                },\n                animate: {\n                    opacity: isHovered ? 1 : 0\n                },\n                transition: {\n                    duration: 0.3\n                }\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/InteractiveCard.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    transform: \"translateZ(50px)\"\n                },\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/InteractiveCard.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/InteractiveCard.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(InteractiveCard, \"iFkauXwPQSyqJzLdtZqWpOIZ1D8=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useMotionValue,\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useMotionValue,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useSpring,\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform\n    ];\n});\n_c = InteractiveCard;\nfunction MagneticButton(param) {\n    let { children, className = \"\", magnetStrength = 0.3 } = param;\n    _s1();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const handleMouseMove = (e)=>{\n        if (!ref.current) return;\n        const rect = ref.current.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n        const deltaX = (e.clientX - centerX) * magnetStrength;\n        const deltaY = (e.clientY - centerY) * magnetStrength;\n        setPosition({\n            x: deltaX,\n            y: deltaY\n        });\n    };\n    const handleMouseLeave = ()=>{\n        setPosition({\n            x: 0,\n            y: 0\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n        ref: ref,\n        onMouseMove: handleMouseMove,\n        onMouseLeave: handleMouseLeave,\n        animate: {\n            x: position.x,\n            y: position.y\n        },\n        transition: {\n            type: \"spring\",\n            stiffness: 200,\n            damping: 20\n        },\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/InteractiveCard.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s1(MagneticButton, \"5o4ehqgmojCHPSmxMWTPGKSjVtk=\");\n_c1 = MagneticButton;\nfunction FloatingElement(param) {\n    let { children, className = \"\", amplitude = 20, duration = 3, delay = 0 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        className: className,\n        animate: {\n            y: [\n                -amplitude,\n                amplitude,\n                -amplitude\n            ]\n        },\n        transition: {\n            duration,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/InteractiveCard.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_c2 = FloatingElement;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"InteractiveCard\");\n$RefreshReg$(_c1, \"MagneticButton\");\n$RefreshReg$(_c2, \"FloatingElement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/InteractiveCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-spring.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSpring: function() { return /* binding */ useSpring; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/is-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/utils/is-motion-value.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _animation_animators_js_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../animation/animators/js/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs\");\n/* harmony import */ var _utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/time-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\n\n/**\n * Creates a `MotionValue` that, when `set`, will use a spring animation to animate to its new state.\n *\n * It can either work as a stand-alone `MotionValue` by initialising it with a value, or as a subscriber\n * to another `MotionValue`.\n *\n * @remarks\n *\n * ```jsx\n * const x = useSpring(0, { stiffness: 300 })\n * const y = useSpring(x, { damping: 10 })\n * ```\n *\n * @param inputValue - `MotionValue` or number. If provided a `MotionValue`, when the input `MotionValue` changes, the created `MotionValue` will spring towards that value.\n * @param springConfig - Configuration options for the spring.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction useSpring(source, config = {}) {\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionConfigContext);\n    const activeSpringAnimation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_2__.useMotionValue)((0,_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_3__.isMotionValue)(source) ? source.get() : source);\n    const stopAnimation = () => {\n        if (activeSpringAnimation.current) {\n            activeSpringAnimation.current.stop();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        return value.attach((v, set) => {\n            /**\n             * A more hollistic approach to this might be to use isStatic to fix VisualElement animations\n             * at that level, but this will work for now\n             */\n            if (isStatic)\n                return set(v);\n            stopAnimation();\n            activeSpringAnimation.current = (0,_animation_animators_js_index_mjs__WEBPACK_IMPORTED_MODULE_4__.animateValue)({\n                keyframes: [value.get(), v],\n                velocity: value.getVelocity(),\n                type: \"spring\",\n                restDelta: 0.001,\n                restSpeed: 0.01,\n                ...config,\n                onUpdate: set,\n            });\n            /**\n             * If we're between frames, resync the animation to the frameloop.\n             */\n            if (!_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_5__.frameData.isProcessing) {\n                const delta = performance.now() - _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_5__.frameData.timestamp;\n                if (delta < 30) {\n                    activeSpringAnimation.current.time =\n                        (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_6__.millisecondsToSeconds)(delta);\n                }\n            }\n            return value.get();\n        }, stopAnimation);\n    }, [JSON.stringify(config)]);\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(() => {\n        if ((0,_utils_is_motion_value_mjs__WEBPACK_IMPORTED_MODULE_3__.isMotionValue)(source)) {\n            return source.on(\"change\", (v) => value.set(parseFloat(v)));\n        }\n    }, [value]);\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLXNwcmluZy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQStEO0FBQ0g7QUFDSjtBQUNpQjtBQUNNO0FBQ1o7QUFDRTtBQUNsQjs7QUFFbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGdCQUFnQjtBQUM1Qyw0QkFBNEIsYUFBYTtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDLFlBQVksV0FBVyxFQUFFLGlEQUFVLENBQUMsaUZBQW1CO0FBQ3ZELGtDQUFrQyw2Q0FBTTtBQUN4QyxrQkFBa0IscUVBQWMsQ0FBQyx5RUFBYTtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSx5REFBa0I7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QywrRUFBWTtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsMkRBQVM7QUFDMUIsa0RBQWtELDJEQUFTO0FBQzNEO0FBQ0E7QUFDQSx3QkFBd0IsaUZBQXFCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0wsSUFBSSwyRkFBeUI7QUFDN0IsWUFBWSx5RUFBYTtBQUN6QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRXFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLXNwcmluZy5tanM/ZDI0MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDb250ZXh0LCB1c2VSZWYsIHVzZUluc2VydGlvbkVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGlzTW90aW9uVmFsdWUgfSBmcm9tICcuL3V0aWxzL2lzLW1vdGlvbi12YWx1ZS5tanMnO1xuaW1wb3J0IHsgdXNlTW90aW9uVmFsdWUgfSBmcm9tICcuL3VzZS1tb3Rpb24tdmFsdWUubWpzJztcbmltcG9ydCB7IE1vdGlvbkNvbmZpZ0NvbnRleHQgfSBmcm9tICcuLi9jb250ZXh0L01vdGlvbkNvbmZpZ0NvbnRleHQubWpzJztcbmltcG9ydCB7IHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QgfSBmcm9tICcuLi91dGlscy91c2UtaXNvbW9ycGhpYy1lZmZlY3QubWpzJztcbmltcG9ydCB7IGFuaW1hdGVWYWx1ZSB9IGZyb20gJy4uL2FuaW1hdGlvbi9hbmltYXRvcnMvanMvaW5kZXgubWpzJztcbmltcG9ydCB7IG1pbGxpc2Vjb25kc1RvU2Vjb25kcyB9IGZyb20gJy4uL3V0aWxzL3RpbWUtY29udmVyc2lvbi5tanMnO1xuaW1wb3J0IHsgZnJhbWVEYXRhIH0gZnJvbSAnLi4vZnJhbWVsb29wL2ZyYW1lLm1qcyc7XG5cbi8qKlxuICogQ3JlYXRlcyBhIGBNb3Rpb25WYWx1ZWAgdGhhdCwgd2hlbiBgc2V0YCwgd2lsbCB1c2UgYSBzcHJpbmcgYW5pbWF0aW9uIHRvIGFuaW1hdGUgdG8gaXRzIG5ldyBzdGF0ZS5cbiAqXG4gKiBJdCBjYW4gZWl0aGVyIHdvcmsgYXMgYSBzdGFuZC1hbG9uZSBgTW90aW9uVmFsdWVgIGJ5IGluaXRpYWxpc2luZyBpdCB3aXRoIGEgdmFsdWUsIG9yIGFzIGEgc3Vic2NyaWJlclxuICogdG8gYW5vdGhlciBgTW90aW9uVmFsdWVgLlxuICpcbiAqIEByZW1hcmtzXG4gKlxuICogYGBganN4XG4gKiBjb25zdCB4ID0gdXNlU3ByaW5nKDAsIHsgc3RpZmZuZXNzOiAzMDAgfSlcbiAqIGNvbnN0IHkgPSB1c2VTcHJpbmcoeCwgeyBkYW1waW5nOiAxMCB9KVxuICogYGBgXG4gKlxuICogQHBhcmFtIGlucHV0VmFsdWUgLSBgTW90aW9uVmFsdWVgIG9yIG51bWJlci4gSWYgcHJvdmlkZWQgYSBgTW90aW9uVmFsdWVgLCB3aGVuIHRoZSBpbnB1dCBgTW90aW9uVmFsdWVgIGNoYW5nZXMsIHRoZSBjcmVhdGVkIGBNb3Rpb25WYWx1ZWAgd2lsbCBzcHJpbmcgdG93YXJkcyB0aGF0IHZhbHVlLlxuICogQHBhcmFtIHNwcmluZ0NvbmZpZyAtIENvbmZpZ3VyYXRpb24gb3B0aW9ucyBmb3IgdGhlIHNwcmluZy5cbiAqIEByZXR1cm5zIGBNb3Rpb25WYWx1ZWBcbiAqXG4gKiBAcHVibGljXG4gKi9cbmZ1bmN0aW9uIHVzZVNwcmluZyhzb3VyY2UsIGNvbmZpZyA9IHt9KSB7XG4gICAgY29uc3QgeyBpc1N0YXRpYyB9ID0gdXNlQ29udGV4dChNb3Rpb25Db25maWdDb250ZXh0KTtcbiAgICBjb25zdCBhY3RpdmVTcHJpbmdBbmltYXRpb24gPSB1c2VSZWYobnVsbCk7XG4gICAgY29uc3QgdmFsdWUgPSB1c2VNb3Rpb25WYWx1ZShpc01vdGlvblZhbHVlKHNvdXJjZSkgPyBzb3VyY2UuZ2V0KCkgOiBzb3VyY2UpO1xuICAgIGNvbnN0IHN0b3BBbmltYXRpb24gPSAoKSA9PiB7XG4gICAgICAgIGlmIChhY3RpdmVTcHJpbmdBbmltYXRpb24uY3VycmVudCkge1xuICAgICAgICAgICAgYWN0aXZlU3ByaW5nQW5pbWF0aW9uLmN1cnJlbnQuc3RvcCgpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICB1c2VJbnNlcnRpb25FZmZlY3QoKCkgPT4ge1xuICAgICAgICByZXR1cm4gdmFsdWUuYXR0YWNoKCh2LCBzZXQpID0+IHtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogQSBtb3JlIGhvbGxpc3RpYyBhcHByb2FjaCB0byB0aGlzIG1pZ2h0IGJlIHRvIHVzZSBpc1N0YXRpYyB0byBmaXggVmlzdWFsRWxlbWVudCBhbmltYXRpb25zXG4gICAgICAgICAgICAgKiBhdCB0aGF0IGxldmVsLCBidXQgdGhpcyB3aWxsIHdvcmsgZm9yIG5vd1xuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBpZiAoaXNTdGF0aWMpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHNldCh2KTtcbiAgICAgICAgICAgIHN0b3BBbmltYXRpb24oKTtcbiAgICAgICAgICAgIGFjdGl2ZVNwcmluZ0FuaW1hdGlvbi5jdXJyZW50ID0gYW5pbWF0ZVZhbHVlKHtcbiAgICAgICAgICAgICAgICBrZXlmcmFtZXM6IFt2YWx1ZS5nZXQoKSwgdl0sXG4gICAgICAgICAgICAgICAgdmVsb2NpdHk6IHZhbHVlLmdldFZlbG9jaXR5KCksXG4gICAgICAgICAgICAgICAgdHlwZTogXCJzcHJpbmdcIixcbiAgICAgICAgICAgICAgICByZXN0RGVsdGE6IDAuMDAxLFxuICAgICAgICAgICAgICAgIHJlc3RTcGVlZDogMC4wMSxcbiAgICAgICAgICAgICAgICAuLi5jb25maWcsXG4gICAgICAgICAgICAgICAgb25VcGRhdGU6IHNldCxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJZiB3ZSdyZSBiZXR3ZWVuIGZyYW1lcywgcmVzeW5jIHRoZSBhbmltYXRpb24gdG8gdGhlIGZyYW1lbG9vcC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKCFmcmFtZURhdGEuaXNQcm9jZXNzaW5nKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZGVsdGEgPSBwZXJmb3JtYW5jZS5ub3coKSAtIGZyYW1lRGF0YS50aW1lc3RhbXA7XG4gICAgICAgICAgICAgICAgaWYgKGRlbHRhIDwgMzApIHtcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlU3ByaW5nQW5pbWF0aW9uLmN1cnJlbnQudGltZSA9XG4gICAgICAgICAgICAgICAgICAgICAgICBtaWxsaXNlY29uZHNUb1NlY29uZHMoZGVsdGEpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB2YWx1ZS5nZXQoKTtcbiAgICAgICAgfSwgc3RvcEFuaW1hdGlvbik7XG4gICAgfSwgW0pTT04uc3RyaW5naWZ5KGNvbmZpZyldKTtcbiAgICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKGlzTW90aW9uVmFsdWUoc291cmNlKSkge1xuICAgICAgICAgICAgcmV0dXJuIHNvdXJjZS5vbihcImNoYW5nZVwiLCAodikgPT4gdmFsdWUuc2V0KHBhcnNlRmxvYXQodikpKTtcbiAgICAgICAgfVxuICAgIH0sIFt2YWx1ZV0pO1xuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuZXhwb3J0IHsgdXNlU3ByaW5nIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\n"));

/***/ })

});