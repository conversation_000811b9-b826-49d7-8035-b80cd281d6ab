"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/Hero */ \"(app-pages-browser)/./src/components/sections/Hero.tsx\");\n/* harmony import */ var _components_sections_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/About */ \"(app-pages-browser)/./src/components/sections/About.tsx\");\n/* harmony import */ var _components_sections_Services__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/Services */ \"(app-pages-browser)/./src/components/sections/Services.tsx\");\n/* harmony import */ var _components_sections_Portfolio__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/Portfolio */ \"(app-pages-browser)/./src/components/sections/Portfolio.tsx\");\n/* harmony import */ var _components_sections_Testimonials__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sections/Testimonials */ \"(app-pages-browser)/./src/components/sections/Testimonials.tsx\");\n/* harmony import */ var _components_sections_FAQ__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/sections/FAQ */ \"(app-pages-browser)/./src/components/sections/FAQ.tsx\");\n/* harmony import */ var _components_sections_Contact__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/sections/Contact */ \"(app-pages-browser)/./src/components/sections/Contact.tsx\");\n/* harmony import */ var _components_layout_Navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/layout/Navigation */ \"(app-pages-browser)/./src/components/layout/Navigation.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Register GSAP plugins\nif (true) {\n    gsap__WEBPACK_IMPORTED_MODULE_11__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_12__.ScrollTrigger);\n}\nfunction Home() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize GSAP animations\n        gsap__WEBPACK_IMPORTED_MODULE_11__.gsap.fromTo(\".fade-in-up\", {\n            y: 100,\n            opacity: 0\n        }, {\n            y: 0,\n            opacity: 1,\n            duration: 1,\n            stagger: 0.2,\n            scrollTrigger: {\n                trigger: \".fade-in-up\",\n                start: \"top 80%\",\n                end: \"bottom 20%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n        // Parallax effect for hero section\n        gsap__WEBPACK_IMPORTED_MODULE_11__.gsap.to(\".parallax-bg\", {\n            yPercent: -50,\n            ease: \"none\",\n            scrollTrigger: {\n                trigger: \".parallax-bg\",\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: true\n            }\n        });\n        // Text reveal animation\n        gsap__WEBPACK_IMPORTED_MODULE_11__.gsap.fromTo(\".text-reveal\", {\n            y: 100,\n            opacity: 0\n        }, {\n            y: 0,\n            opacity: 1,\n            duration: 1.2,\n            ease: \"power3.out\",\n            scrollTrigger: {\n                trigger: \".text-reveal\",\n                start: \"top 85%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n        return ()=>{\n            gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_12__.ScrollTrigger.getAll().forEach((trigger)=>trigger.kill());\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navigation__WEBPACK_IMPORTED_MODULE_9__.Navigation, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__.Hero, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_About__WEBPACK_IMPORTED_MODULE_3__.About, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Services__WEBPACK_IMPORTED_MODULE_4__.Services, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Portfolio__WEBPACK_IMPORTED_MODULE_5__.Portfolio, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Testimonials__WEBPACK_IMPORTED_MODULE_6__.Testimonials, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_FAQ__WEBPACK_IMPORTED_MODULE_7__.FAQ, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Contact__WEBPACK_IMPORTED_MODULE_8__.Contact, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_10__.Footer, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/About.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/About.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   About: function() { return /* binding */ About; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _components_ui_ParallaxSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ParallaxSection */ \"(app-pages-browser)/./src/components/ui/ParallaxSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ About auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction About() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__.ScrollTrigger);\n            // Text reveal animation\n            gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.fromTo(\".about-text\", {\n                y: 50,\n                opacity: 0\n            }, {\n                y: 0,\n                opacity: 1,\n                duration: 1,\n                stagger: 0.2,\n                scrollTrigger: {\n                    trigger: sectionRef.current,\n                    start: \"top 80%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n            // Stats counter animation\n            gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.fromTo(\".stat-number\", {\n                innerText: 0\n            }, {\n                innerText: (i, target)=>target.getAttribute(\"data-value\"),\n                duration: 2,\n                ease: \"power2.out\",\n                snap: {\n                    innerText: 1\n                },\n                scrollTrigger: {\n                    trigger: \".stats-container\",\n                    start: \"top 80%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n        }\n    }, []);\n    const stats = [\n        {\n            number: \"50\",\n            label: \"დასრულებული პროექტი\",\n            suffix: \"+\"\n        },\n        {\n            number: \"25\",\n            label: \"კმაყოფილი კლიენტი\",\n            suffix: \"+\"\n        },\n        {\n            number: \"3\",\n            label: \"წლიანი გამოცდილება\",\n            suffix: \"+\"\n        },\n        {\n            number: \"24\",\n            label: \"საათიანი მხარდაჭერა\",\n            suffix: \"/7\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        id: \"about\",\n        className: \"py-20 lg:py-32 bg-white dark:bg-dark-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 w-72 h-72 bg-primary-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-10 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-16 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"about-text\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\",\n                                            children: [\n                                                \"ჩვენი \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gradient\",\n                                                    children: \"ისტორია\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-600 dark:text-gray-400 leading-relaxed mb-6\",\n                                            children: \"Devora Dev არის თანამედროვე ვებ სტუდია, რომელიც 2021 წელს დაარსდა ციფრული ტექნოლოგიების მიმართ ვნებით. ჩვენი მისიაა ბიზნესებისთვის უნიკალური და ეფექტური ციფრული გადაწყვეტების შექმნა.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-gray-600 dark:text-gray-400 leading-relaxed\",\n                                            children: \"ჩვენ ვთანამშრომლობთ როგორც მცირე სტარტაპებთან, ასევე დიდ კორპორაციებთან, ყოველთვის ვცდილობთ გადავაჭარბოთ მოლოდინებს და შევქმნათ პროდუქტები, რომლებიც ნამდვილად ცვლის ბიზნესის მიმართულებას.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    className: \"about-text space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-semibold text-gray-900 dark:text-white mb-4\",\n                                            children: \"ჩვენი ღირებულებები\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                {\n                                                    icon: \"\\uD83C\\uDFAF\",\n                                                    title: \"ინოვაცია\",\n                                                    desc: \"ყოველთვის ვიყენებთ უახლეს ტექნოლოგიებს\"\n                                                },\n                                                {\n                                                    icon: \"\\uD83E\\uDD1D\",\n                                                    title: \"პარტნიორობა\",\n                                                    desc: \"კლიენტებთან გრძელვადიანი ურთიერთობა\"\n                                                },\n                                                {\n                                                    icon: \"⚡\",\n                                                    title: \"ხარისხი\",\n                                                    desc: \"უმაღლესი ხარისხის სტანდარტები\"\n                                                }\n                                            ].map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: value.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                    children: value.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                                    lineNumber: 116,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-400\",\n                                                                    children: value.desc\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"stats-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.9\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                className: \"bg-gradient-to-br from-primary-50 to-purple-50 dark:from-primary-900/20 dark:to-purple-900/20 rounded-3xl p-8 lg:p-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center\",\n                                        children: \"ჩვენი მიღწევები\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-8\",\n                                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: index * 0.1\n                                                },\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl lg:text-4xl font-bold text-primary-500 mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxSection__WEBPACK_IMPORTED_MODULE_2__.CounterAnimation, {\n                                                            from: 0,\n                                                            to: parseInt(stat.number),\n                                                            suffix: stat.suffix,\n                                                            duration: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400 text-sm lg:text-base\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-12 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-gray-900 dark:text-white mb-6\",\n                                                children: \"ჩვენი გუნდი\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center space-x-4\",\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3,\n                                                    4\n                                                ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        className: \"w-12 h-12 bg-gradient-to-br from-primary-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                        children: String.fromCharCode(65 + index)\n                                                    }, index, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 mt-4\",\n                                                children: \"გამოცდილი პროფესიონალების გუნდი\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/About.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n_s(About, \"O9MYfDkQexHh+zrn37J6HLSAdf8=\");\n_c = About;\nvar _c;\n$RefreshReg$(_c, \"About\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/About.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/FAQ.tsx":
/*!*****************************************!*\
  !*** ./src/components/sections/FAQ.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FAQ: function() { return /* binding */ FAQ; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_ui_ParallaxSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ParallaxSection */ \"(app-pages-browser)/./src/components/ui/ParallaxSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ FAQ auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction FAQ() {\n    _s();\n    const [openFAQ, setOpenFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const faqs = [\n        {\n            question: \"რამდენ ხანში მზადდება ვებსაიტი?\",\n            answer: \"ვებსაიტის განვითარების ვადები დამოკიდებულია პროექტის სირთულეზე. მარტივი ვებსაიტი 2-3 კვირაში, რთული ვებ აპლიკაცია კი 2-3 თვეში მზადდება. ყოველი პროექტისთვის ინდივიდუალურად განვსაზღვრავთ ვადებს.\"\n        },\n        {\n            question: \"რა ღირს ვებსაიტის შექმნა?\",\n            answer: \"ფასი დამოკიდებულია პროექტის მოთხოვნებზე. მარტივი ვებსაიტი იწყება 1500 ლარიდან, ხოლო რთული ვებ აპლიკაციები 5000 ლარიდან. ზუსტი ფასის გასაცნობად დაგვიკავშირდით უფასო კონსულტაციისთვის.\"\n        },\n        {\n            question: \"გაქვთ თუ არა მხარდაჭერის სერვისი?\",\n            answer: \"დიახ, ჩვენ ვთავაზობთ 24/7 ტექნიკურ მხარდაჭერას. პროექტის დასრულების შემდეგ 3 თვის განმავლობაში უფასო მხარდაჭერა ვრცელდება, შემდეგ კი შეგიძლიათ შეიძინოთ მხარდაჭერის პაკეტი.\"\n        },\n        {\n            question: \"შეგიძლიათ არსებული ვებსაიტის გაუმჯობესება?\",\n            answer: \"რა თქმა უნდა! ჩვენ ვთავაზობთ არსებული ვებსაიტების რედიზაინს, ოპტიმიზაციას და ახალი ფუნქციების დამატებას. პირველ რიგში ვაკეთებთ ანალიზს და შემდეგ ვთავაზობთ გაუმჯობესების გეგმას.\"\n        },\n        {\n            question: \"რა ტექნოლოგიებს იყენებთ?\",\n            answer: \"ჩვენ ვიყენებთ უახლეს ტექნოლოგიებს: React, Next.js, Node.js, TypeScript, Python, PHP/Laravel, MongoDB, PostgreSQL და სხვა. ყოველი პროექტისთვის ვირჩევთ ყველაზე შესაფერის ტექნოლოგიურ სტეკს.\"\n        },\n        {\n            question: \"გაქვთ SEO ოპტიმიზაციის სერვისი?\",\n            answer: \"დიახ, ჩვენ ვთავაზობთ სრულ SEO სერვისს: ტექნიკური SEO, კონტენტ ოპტიმიზაცია, კეთილშობილი ლინკების შექმნა, ანალიტიკა და რეგულარული რეპორტები. ჩვენი მიზანია თქვენი საიტის ხილვადობის მაქსიმალური გაზრდა.\"\n        },\n        {\n            question: \"შეგიძლიათ მობილური აპლიკაციის შექმნა?\",\n            answer: \"დიახ, ჩვენ ვქმნით როგორც ნატიურ (iOS/Android), ასევე კროს-პლატფორმულ მობილურ აპლიკაციებს React Native და Flutter-ის გამოყენებით. ასევე ვთავაზობთ App Store-ში და Google Play-ში გამოქვეყნების სერვისს.\"\n        },\n        {\n            question: \"რა არის თქვენი სამუშაო პროცესი?\",\n            answer: \"ჩვენი პროცესი მოიცავს 5 ეტაპს: 1) კონსულტაცია და მოთხოვნების ანალიზი, 2) დიზაინის შექმნა და დამტკიცება, 3) განვითარება და ტესტირება, 4) გაშვება და ოპტიმიზაცია, 5) მხარდაჭერა და მონიტორინგი.\"\n        }\n    ];\n    const toggleFAQ = (index)=>{\n        setOpenFAQ(openFAQ === index ? null : index);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 lg:py-32 bg-white dark:bg-dark-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-64 h-64 bg-primary-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 left-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxSection__WEBPACK_IMPORTED_MODULE_2__.ScrollReveal, {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: [\n                                    \"ხშირად დასმული \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gradient\",\n                                        children: \"კითხვები\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 28\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-400\",\n                                children: \"იპოვეთ პასუხები ყველაზე ხშირად დასმულ კითხვებზე\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxSection__WEBPACK_IMPORTED_MODULE_2__.ScrollReveal, {\n                                delay: index * 0.1,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"bg-gray-50 dark:bg-dark-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300\",\n                                    whileHover: {\n                                        scale: 1.01\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            onClick: ()=>toggleFAQ(index),\n                                            className: \"w-full px-6 py-6 text-left flex items-center justify-between focus:outline-none\",\n                                            whileTap: {\n                                                scale: 0.99\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-white pr-4\",\n                                                    children: faq.question\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                    animate: {\n                                                        rotate: openFAQ === index ? 45 : 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    className: \"flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                                            children: openFAQ === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                initial: {\n                                                    height: 0,\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    height: \"auto\",\n                                                    opacity: 1\n                                                },\n                                                exit: {\n                                                    height: 0,\n                                                    opacity: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.3\n                                                },\n                                                className: \"overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-6 pb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-px bg-gray-200 dark:bg-gray-700 mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 leading-relaxed\",\n                                                            children: faq.answer\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, index, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxSection__WEBPACK_IMPORTED_MODULE_2__.ScrollReveal, {\n                        className: \"text-center mt-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-400 mb-6\",\n                                children: \"ვერ იპოვეთ პასუხი თქვენს კითხვაზე?\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05,\n                                    y: -2\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"px-8 py-4 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300\",\n                                children: \"დაგვიკავშირდით\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/FAQ.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(FAQ, \"D9F26P9PozOEGAGjm6D8F1QbUew=\");\n_c = FAQ;\nvar _c;\n$RefreshReg$(_c, \"FAQ\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/FAQ.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/Services.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Services.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Services: function() { return /* binding */ Services; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ Services auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Services() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\n            // Service cards animation\n            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(\".service-card\", {\n                y: 80,\n                opacity: 0,\n                scale: 0.9\n            }, {\n                y: 0,\n                opacity: 1,\n                scale: 1,\n                duration: 0.8,\n                stagger: 0.2,\n                ease: \"power3.out\",\n                scrollTrigger: {\n                    trigger: sectionRef.current,\n                    start: \"top 70%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n        }\n    }, []);\n    const services = [\n        {\n            icon: \"\\uD83C\\uDFA8\",\n            title: \"UI/UX დიზაინი\",\n            description: \"მომხმარებელზე ორიენტირებული, ინტუიტიური და ვიზუალურად მიმზიდველი დიზაინები\",\n            features: [\n                \"User Research\",\n                \"Wireframing\",\n                \"Prototyping\",\n                \"Visual Design\"\n            ],\n            color: \"from-pink-500 to-rose-500\"\n        },\n        {\n            icon: \"\\uD83D\\uDCBB\",\n            title: \"ვებ განვითარება\",\n            description: \"თანამედროვე, რესპონსიული და ოპტიმიზებული ვებსაიტები და ვებ აპლიკაციები\",\n            features: [\n                \"React/Next.js\",\n                \"Node.js\",\n                \"Database Design\",\n                \"API Development\"\n            ],\n            color: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            icon: \"\\uD83D\\uDCF1\",\n            title: \"მობილური აპები\",\n            description: \"iOS და Android პლატფორმებისთვის ნატიური და კროს-პლატფორმული აპლიკაციები\",\n            features: [\n                \"React Native\",\n                \"Flutter\",\n                \"Native Development\",\n                \"App Store Optimization\"\n            ],\n            color: \"from-green-500 to-emerald-500\"\n        },\n        {\n            icon: \"\\uD83D\\uDE80\",\n            title: \"SEO ოპტიმიზაცია\",\n            description: \"საძიებო სისტემებში თქვენი საიტის ხილვადობის გაზრდა და ორგანული ტრაფიკის მოზიდვა\",\n            features: [\n                \"Technical SEO\",\n                \"Content Strategy\",\n                \"Link Building\",\n                \"Analytics\"\n            ],\n            color: \"from-purple-500 to-violet-500\"\n        },\n        {\n            icon: \"\\uD83C\\uDFAE\",\n            title: \"თამაშების განვითარება\",\n            description: \"2D და 3D თამაშები ვებისთვის, მობილურისთვის და დესკტოპისთვის\",\n            features: [\n                \"Unity\",\n                \"Unreal Engine\",\n                \"Web Games\",\n                \"Mobile Games\"\n            ],\n            color: \"from-orange-500 to-red-500\"\n        },\n        {\n            icon: \"⚡\",\n            title: \"Performance ოპტიმიზაცია\",\n            description: \"საიტის სიჩქარის გაუმჯობესება და მომხმარებლის გამოცდილების ოპტიმიზაცია\",\n            features: [\n                \"Speed Optimization\",\n                \"Core Web Vitals\",\n                \"CDN Setup\",\n                \"Caching\"\n            ],\n            color: \"from-yellow-500 to-amber-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        id: \"services\",\n        className: \"py-20 lg:py-32 bg-gray-50 dark:bg-dark-800 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-full bg-grid-pattern opacity-5\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 left-20 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: [\n                                    \"ჩვენი \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gradient\",\n                                        children: \"სერვისები\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                children: \"ვთავაზობთ სრულ სპექტრს ციფრული სერვისებისა, რომლებიც თქვენს ბიზნესს ონლაინ სივრცეში წარმატებისკენ მიგიყვანს\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                whileHover: {\n                                    y: -10,\n                                    scale: 1.02\n                                },\n                                className: \"service-card group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-dark-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 h-full border border-gray-200 dark:border-gray-700 hover:border-transparent relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br \".concat(service.color, \" opacity-0 group-hover:opacity-5 transition-opacity duration-300\")\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br \".concat(service.color, \" rounded-2xl flex items-center justify-center text-2xl mb-6 group-hover:scale-110 transition-transform duration-300\"),\n                                                    children: service.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-primary-500 group-hover:to-purple-500 transition-all duration-300\",\n                                                    children: service.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-400 mb-6 leading-relaxed\",\n                                                    children: service.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: service.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center text-sm text-gray-500 dark:text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-gradient-to-r \".concat(service.color, \" rounded-full mr-3\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                feature\n                                                            ]\n                                                        }, featureIndex, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"mt-6 px-6 py-3 bg-gradient-to-r \".concat(service.color, \" text-white font-semibold rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0\"),\n                                                    children: \"შეიტყვეთ მეტი\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            }, index, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.4\n                        },\n                        className: \"text-center mt-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-400 mb-8\",\n                                children: \"არ ხედავთ თქვენთვის საჭირო სერვისს? ჩვენ ვქმნით ინდივიდუალურ გადაწყვეტებს!\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05,\n                                    y: -2\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"px-8 py-4 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300\",\n                                children: \"დაგვიკავშირდით კონსულტაციისთვის\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(Services, \"O9MYfDkQexHh+zrn37J6HLSAdf8=\");\n_c = Services;\nvar _c;\n$RefreshReg$(_c, \"Services\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Services.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/Testimonials.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/Testimonials.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Testimonials: function() { return /* binding */ Testimonials; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _components_ui_ParallaxSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ParallaxSection */ \"(app-pages-browser)/./src/components/ui/ParallaxSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ Testimonials auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Testimonials() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeTestimonial, setActiveTestimonial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const testimonials = [\n        {\n            id: 1,\n            name: \"ნინო გელაშვილი\",\n            position: \"CEO, TechStart\",\n            company: \"TechStart\",\n            image: \"/api/placeholder/80/80\",\n            rating: 5,\n            text: \"Devora Dev-მა ჩვენი კომპანიის ვებსაიტი სრულიად გარდაქმნა. მათი პროფესიონალიზმი და ყურადღება დეტალებზე უბრალოდ შთამბეჭდავია. ახლა ჩვენი ონლაინ გაყიდვები 300%-ით გაიზარდა!\",\n            project: \"E-commerce Platform\"\n        },\n        {\n            id: 2,\n            name: \"გიორგი მამაცაშვილი\",\n            position: \"მარკეტინგის დირექტორი\",\n            company: \"Digital Agency\",\n            image: \"/api/placeholder/80/80\",\n            rating: 5,\n            text: \"ჩვენი მობილური აპლიკაციის განვითარება Devora Dev-თან იყო ნამდვილი სიამოვნება. მათმა გუნდმა ყველა ჩვენი მოლოდინი გადააჭარბა და შედეგი უბრალოდ შესანიშნავია.\",\n            project: \"Mobile Banking App\"\n        },\n        {\n            id: 3,\n            name: \"ანა ხარაიშვილი\",\n            position: \"ფაუნდერი\",\n            company: \"Creative Studio\",\n            image: \"/api/placeholder/80/80\",\n            rating: 5,\n            text: \"SEO ოპტიმიზაციის შემდეგ ჩვენი საიტი Google-ის პირველ გვერდზე აღმოჩნდა. ორგანული ტრაფიკი 500%-ით გაიზარდა. Devora Dev-ის გუნდი ნამდვილად იცის საქმე!\",\n            project: \"SEO Optimization\"\n        },\n        {\n            id: 4,\n            name: \"დავით ლომიძე\",\n            position: \"CTO\",\n            company: \"FinTech Solutions\",\n            image: \"/api/placeholder/80/80\",\n            rating: 5,\n            text: \"ტექნიკური კომპლექსურობის მიუხედავად, Devora Dev-მა ჩვენი ფინტექ პლატფორმა დროულად და ბიუჯეტის ფარგლებში შექმნა. მათი ექსპერტიზა უბრალოდ შეუდარებელია.\",\n            project: \"FinTech Platform\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__.ScrollTrigger);\n            // Auto-rotate testimonials\n            const interval = setInterval(()=>{\n                setActiveTestimonial((prev)=>(prev + 1) % testimonials.length);\n            }, 5000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        testimonials.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 lg:py-32 bg-gradient-to-br from-primary-50 to-purple-50 dark:from-primary-900/10 dark:to-purple-900/10 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 w-72 h-72 bg-primary-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-10 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxSection__WEBPACK_IMPORTED_MODULE_2__.ScrollReveal, {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: [\n                                    \"რას ამბობენ ჩვენი \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gradient\",\n                                        children: \"კლიენტები\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 31\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                children: \"ჩვენი კლიენტების წარმატება არის ჩვენი მთავარი მიზანი\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto mb-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                            mode: \"wait\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -50\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                className: \"bg-white dark:bg-dark-900 rounded-3xl p-8 lg:p-12 shadow-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mb-6\",\n                                        children: [\n                                            ...Array(testimonials[activeTestimonial].rating)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                transition: {\n                                                    delay: i * 0.1\n                                                },\n                                                className: \"text-yellow-400 text-2xl\",\n                                                children: \"⭐\"\n                                            }, i, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                        className: \"text-xl lg:text-2xl text-gray-700 dark:text-gray-300 text-center mb-8 leading-relaxed\",\n                                        children: [\n                                            '\"',\n                                            testimonials[activeTestimonial].text,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl\",\n                                                children: testimonials[activeTestimonial].name.charAt(0)\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 dark:text-white text-lg\",\n                                                        children: testimonials[activeTestimonial].name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: testimonials[activeTestimonial].position\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-primary-500 font-medium\",\n                                                        children: testimonials[activeTestimonial].company\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-4 py-2 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium\",\n                                            children: [\n                                                \"პროექტი: \",\n                                                testimonials[activeTestimonial].project\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, activeTestimonial, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4 mb-16\",\n                        children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                onClick: ()=>setActiveTestimonial(index),\n                                className: \"w-4 h-4 rounded-full transition-all duration-300 \".concat(index === activeTestimonial ? \"bg-primary-500 scale-125\" : \"bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500\"),\n                                whileHover: {\n                                    scale: 1.2\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                }\n                            }, index, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxSection__WEBPACK_IMPORTED_MODULE_2__.ScrollReveal, {\n                                delay: index * 0.1,\n                                className: \"cursor-pointer\",\n                                onClick: ()=>setActiveTestimonial(index),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.05,\n                                        y: -5\n                                    },\n                                    className: \"bg-white dark:bg-dark-900 rounded-2xl p-6 shadow-lg transition-all duration-300 \".concat(index === activeTestimonial ? \"ring-2 ring-primary-500 shadow-xl\" : \"hover:shadow-xl\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold\",\n                                                    children: testimonial.name.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                                            children: testimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: testimonial.company\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 dark:text-gray-300 text-sm line-clamp-3\",\n                                            children: testimonial.text\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            }, testimonial.id, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Testimonials.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(Testimonials, \"ApAzFFe+pxoVDLnPFPK40aq6OpI=\");\n_c = Testimonials;\nvar _c;\n$RefreshReg$(_c, \"Testimonials\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Testimonials.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/ParallaxSection.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/ParallaxSection.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CounterAnimation: function() { return /* binding */ CounterAnimation; },\n/* harmony export */   ParallaxSection: function() { return /* binding */ ParallaxSection; },\n/* harmony export */   ScrollReveal: function() { return /* binding */ ScrollReveal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ ParallaxSection,ScrollReveal,CounterAnimation auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\nfunction ParallaxSection(param) {\n    let { children, speed = 0.5, className = \"\", direction = \"up\" } = param;\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: ref,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], direction === \"up\" ? [\n        \"0%\",\n        \"-50%\"\n    ] : [\n        \"0%\",\n        \"50%\"\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        ref: ref,\n        style: {\n            y\n        },\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/ParallaxSection.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(ParallaxSection, \"w5ZdYfEZcXaaY6r06Rgcg2ryUN4=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform\n    ];\n});\n_c = ParallaxSection;\nfunction ScrollReveal(param) {\n    let { children, className = \"\", delay = 0, duration = 0.8, distance = 50, direction = \"up\" } = param;\n    _s1();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger);\n            const element = ref.current;\n            if (!element) return;\n            const directionMap = {\n                up: {\n                    y: distance,\n                    x: 0\n                },\n                down: {\n                    y: -distance,\n                    x: 0\n                },\n                left: {\n                    x: distance,\n                    y: 0\n                },\n                right: {\n                    x: -distance,\n                    y: 0\n                }\n            };\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.fromTo(element, {\n                ...directionMap[direction],\n                opacity: 0\n            }, {\n                x: 0,\n                y: 0,\n                opacity: 1,\n                duration,\n                delay,\n                ease: \"power3.out\",\n                scrollTrigger: {\n                    trigger: element,\n                    start: \"top 85%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n        }\n    }, [\n        delay,\n        duration,\n        distance,\n        direction\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/ParallaxSection.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s1(ScrollReveal, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c1 = ScrollReveal;\nfunction CounterAnimation(param) {\n    let { from, to, duration = 2, suffix = \"\", className = \"\" } = param;\n    _s2();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger);\n            const element = ref.current;\n            if (!element) return;\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.fromTo({\n                value: from\n            }, {\n                value: to,\n                duration,\n                ease: \"power2.out\",\n                onUpdate: function() {\n                    if (element) {\n                        element.textContent = Math.round(this.targets()[0].value) + suffix;\n                    }\n                },\n                scrollTrigger: {\n                    trigger: element,\n                    start: \"top 80%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n        }\n    }, [\n        from,\n        to,\n        duration,\n        suffix\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        ref: ref,\n        className: className,\n        children: [\n            from,\n            suffix\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/ui/ParallaxSection.tsx\",\n        lineNumber: 152,\n        columnNumber: 10\n    }, this);\n}\n_s2(CounterAnimation, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\n_c2 = CounterAnimation;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ParallaxSection\");\n$RefreshReg$(_c1, \"ScrollReveal\");\n$RefreshReg$(_c2, \"CounterAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ParallaxSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeElement: function() { return /* binding */ resizeElement; }\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/resolve-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs\");\n\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nfunction getElementSize(target, borderBoxSize) {\n    if (borderBoxSize) {\n        const { inlineSize, blockSize } = borderBoxSize[0];\n        return { width: inlineSize, height: blockSize };\n    }\n    else if (target instanceof SVGElement && \"getBBox\" in target) {\n        return target.getBBox();\n    }\n    else {\n        return {\n            width: target.offsetWidth,\n            height: target.offsetHeight,\n        };\n    }\n}\nfunction notifyTarget({ target, contentRect, borderBoxSize, }) {\n    var _a;\n    (_a = resizeHandlers.get(target)) === null || _a === void 0 ? void 0 : _a.forEach((handler) => {\n        handler({\n            target,\n            contentSize: contentRect,\n            get size() {\n                return getElementSize(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = (0,_utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer === null || observer === void 0 ? void 0 : observer.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.delete(handler);\n            if (!(elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.size)) {\n                observer === null || observer === void 0 ? void 0 : observer.unobserve(element);\n            }\n        });\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeWindow: function() { return /* binding */ resizeWindow; }\n/* harmony export */ });\nconst windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const size = {\n            width: window.innerWidth,\n            height: window.innerHeight,\n        };\n        const info = {\n            target: window,\n            size,\n            contentSize: size,\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size && windowResizeHandler) {\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaGFuZGxlLXdpbmRvdy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vcmVzaXplL2hhbmRsZS13aW5kb3cubWpzPzllOTciXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgd2luZG93Q2FsbGJhY2tzID0gbmV3IFNldCgpO1xubGV0IHdpbmRvd1Jlc2l6ZUhhbmRsZXI7XG5mdW5jdGlvbiBjcmVhdGVXaW5kb3dSZXNpemVIYW5kbGVyKCkge1xuICAgIHdpbmRvd1Jlc2l6ZUhhbmRsZXIgPSAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHNpemUgPSB7XG4gICAgICAgICAgICB3aWR0aDogd2luZG93LmlubmVyV2lkdGgsXG4gICAgICAgICAgICBoZWlnaHQ6IHdpbmRvdy5pbm5lckhlaWdodCxcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgaW5mbyA9IHtcbiAgICAgICAgICAgIHRhcmdldDogd2luZG93LFxuICAgICAgICAgICAgc2l6ZSxcbiAgICAgICAgICAgIGNvbnRlbnRTaXplOiBzaXplLFxuICAgICAgICB9O1xuICAgICAgICB3aW5kb3dDYWxsYmFja3MuZm9yRWFjaCgoY2FsbGJhY2spID0+IGNhbGxiYWNrKGluZm8pKTtcbiAgICB9O1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIHdpbmRvd1Jlc2l6ZUhhbmRsZXIpO1xufVxuZnVuY3Rpb24gcmVzaXplV2luZG93KGNhbGxiYWNrKSB7XG4gICAgd2luZG93Q2FsbGJhY2tzLmFkZChjYWxsYmFjayk7XG4gICAgaWYgKCF3aW5kb3dSZXNpemVIYW5kbGVyKVxuICAgICAgICBjcmVhdGVXaW5kb3dSZXNpemVIYW5kbGVyKCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgd2luZG93Q2FsbGJhY2tzLmRlbGV0ZShjYWxsYmFjayk7XG4gICAgICAgIGlmICghd2luZG93Q2FsbGJhY2tzLnNpemUgJiYgd2luZG93UmVzaXplSGFuZGxlcikge1xuICAgICAgICAgICAgd2luZG93UmVzaXplSGFuZGxlciA9IHVuZGVmaW5lZDtcbiAgICAgICAgfVxuICAgIH07XG59XG5cbmV4cG9ydCB7IHJlc2l6ZVdpbmRvdyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resize: function() { return /* binding */ resize; }\n/* harmony export */ });\n/* harmony import */ var _handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./handle-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\");\n/* harmony import */ var _handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handle-window.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\");\n\n\n\nfunction resize(a, b) {\n    return typeof a === \"function\" ? (0,_handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__.resizeWindow)(a) : (0,_handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__.resizeElement)(a, b);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUNGOztBQUVuRDtBQUNBLHFDQUFxQyxnRUFBWSxNQUFNLGtFQUFhO0FBQ3BFOztBQUVrQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vcmVzaXplL2luZGV4Lm1qcz8zMGNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlc2l6ZUVsZW1lbnQgfSBmcm9tICcuL2hhbmRsZS1lbGVtZW50Lm1qcyc7XG5pbXBvcnQgeyByZXNpemVXaW5kb3cgfSBmcm9tICcuL2hhbmRsZS13aW5kb3cubWpzJztcblxuZnVuY3Rpb24gcmVzaXplKGEsIGIpIHtcbiAgICByZXR1cm4gdHlwZW9mIGEgPT09IFwiZnVuY3Rpb25cIiA/IHJlc2l6ZVdpbmRvdyhhKSA6IHJlc2l6ZUVsZW1lbnQoYSwgYik7XG59XG5cbmV4cG9ydCB7IHJlc2l6ZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createScrollInfo: function() { return /* binding */ createScrollInfo; },\n/* harmony export */   updateScrollInfo: function() { return /* binding */ updateScrollInfo; }\n/* harmony export */ });\n/* harmony import */ var _utils_progress_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/progress.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/progress.mjs\");\n/* harmony import */ var _utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/velocity-per-second.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs\");\n\n\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[\"scroll\" + position];\n    axis.scrollLength = element[\"scroll\" + length] - element[\"client\" + length];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = (0,_utils_progress_mjs__WEBPACK_IMPORTED_MODULE_0__.progress)(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : (0,_utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_1__.velocityPerSecond)(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   namedEdges: function() { return /* binding */ namedEdges; },\n/* harmony export */   resolveEdge: function() { return /* binding */ resolveEdge; }\n/* harmony export */ });\nconst namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (namedEdges[edge] !== undefined) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffsets: function() { return /* binding */ resolveOffsets; }\n/* harmony export */ });\n/* harmony import */ var _inset_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./inset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\");\n/* harmony import */ var _presets_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./presets.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\");\n/* harmony import */ var _offset_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\");\n/* harmony import */ var _utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../utils/interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var _utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../utils/offsets/default.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/default.mjs\");\n\n\n\n\n\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    let { offset: offsetDefinition = _presets_mjs__WEBPACK_IMPORTED_MODULE_0__.ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? (0,_inset_mjs__WEBPACK_IMPORTED_MODULE_1__.calcInset)(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = (0,_offset_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffset)(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = (0,_utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_3__.interpolate)(info[axis].offset, (0,_utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__.defaultOffset)(offsetDefinition));\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = info[axis].interpolate(info[axis].current);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcInset: function() { return /* binding */ calcInset; }\n/* harmony export */ });\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if (current instanceof HTMLElement) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2Zmc2V0cy9pbnNldC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLE9BQU87QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vc2Nyb2xsL29mZnNldHMvaW5zZXQubWpzPzYwZTYiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gY2FsY0luc2V0KGVsZW1lbnQsIGNvbnRhaW5lcikge1xuICAgIGNvbnN0IGluc2V0ID0geyB4OiAwLCB5OiAwIH07XG4gICAgbGV0IGN1cnJlbnQgPSBlbGVtZW50O1xuICAgIHdoaWxlIChjdXJyZW50ICYmIGN1cnJlbnQgIT09IGNvbnRhaW5lcikge1xuICAgICAgICBpZiAoY3VycmVudCBpbnN0YW5jZW9mIEhUTUxFbGVtZW50KSB7XG4gICAgICAgICAgICBpbnNldC54ICs9IGN1cnJlbnQub2Zmc2V0TGVmdDtcbiAgICAgICAgICAgIGluc2V0LnkgKz0gY3VycmVudC5vZmZzZXRUb3A7XG4gICAgICAgICAgICBjdXJyZW50ID0gY3VycmVudC5vZmZzZXRQYXJlbnQ7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoY3VycmVudC50YWdOYW1lID09PSBcInN2Z1wiKSB7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFRoaXMgaXNuJ3QgYW4gaWRlYWwgYXBwcm9hY2ggdG8gbWVhc3VyaW5nIHRoZSBvZmZzZXQgb2YgPHN2ZyAvPiB0YWdzLlxuICAgICAgICAgICAgICogSXQgd291bGQgYmUgcHJlZmVyYWJsZSwgZ2l2ZW4gdGhleSBiZWhhdmUgbGlrZSBIVE1MRWxlbWVudHMgaW4gbW9zdCB3YXlzXG4gICAgICAgICAgICAgKiB0byB1c2Ugb2Zmc2V0TGVmdC9Ub3AuIEJ1dCB0aGVzZSBkb24ndCBleGlzdCBvbiA8c3ZnIC8+LiBMaWtld2lzZSB3ZVxuICAgICAgICAgICAgICogY2FuJ3QgdXNlIC5nZXRCQm94KCkgbGlrZSBtb3N0IFNWRyBlbGVtZW50cyBhcyB0aGVzZSBwcm92aWRlIHRoZSBvZmZzZXRcbiAgICAgICAgICAgICAqIHJlbGF0aXZlIHRvIHRoZSBTVkcgaXRzZWxmLCB3aGljaCBmb3IgPHN2ZyAvPiBpcyB1c3VhbGx5IDB4MC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgY29uc3Qgc3ZnQm91bmRpbmdCb3ggPSBjdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgICAgICAgICAgY3VycmVudCA9IGN1cnJlbnQucGFyZW50RWxlbWVudDtcbiAgICAgICAgICAgIGNvbnN0IHBhcmVudEJvdW5kaW5nQm94ID0gY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICAgICAgICAgIGluc2V0LnggKz0gc3ZnQm91bmRpbmdCb3gubGVmdCAtIHBhcmVudEJvdW5kaW5nQm94LmxlZnQ7XG4gICAgICAgICAgICBpbnNldC55ICs9IHN2Z0JvdW5kaW5nQm94LnRvcCAtIHBhcmVudEJvdW5kaW5nQm94LnRvcDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjdXJyZW50IGluc3RhbmNlb2YgU1ZHR3JhcGhpY3NFbGVtZW50KSB7XG4gICAgICAgICAgICBjb25zdCB7IHgsIHkgfSA9IGN1cnJlbnQuZ2V0QkJveCgpO1xuICAgICAgICAgICAgaW5zZXQueCArPSB4O1xuICAgICAgICAgICAgaW5zZXQueSArPSB5O1xuICAgICAgICAgICAgbGV0IHN2ZyA9IG51bGw7XG4gICAgICAgICAgICBsZXQgcGFyZW50ID0gY3VycmVudC5wYXJlbnROb2RlO1xuICAgICAgICAgICAgd2hpbGUgKCFzdmcpIHtcbiAgICAgICAgICAgICAgICBpZiAocGFyZW50LnRhZ05hbWUgPT09IFwic3ZnXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgc3ZnID0gcGFyZW50O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBwYXJlbnQgPSBjdXJyZW50LnBhcmVudE5vZGU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjdXJyZW50ID0gc3ZnO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGluc2V0O1xufVxuXG5leHBvcnQgeyBjYWxjSW5zZXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffset: function() { return /* binding */ resolveOffset; }\n/* harmony export */ });\n/* harmony import */ var _edge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edge.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\");\n\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, _edge_mjs__WEBPACK_IMPORTED_MODULE_0__.namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollOffset: function() { return /* binding */ ScrollOffset; }\n/* harmony export */ });\nconst ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2Zmc2V0cy9wcmVzZXRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vc2Nyb2xsL29mZnNldHMvcHJlc2V0cy5tanM/N2RmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBTY3JvbGxPZmZzZXQgPSB7XG4gICAgRW50ZXI6IFtcbiAgICAgICAgWzAsIDFdLFxuICAgICAgICBbMSwgMV0sXG4gICAgXSxcbiAgICBFeGl0OiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDBdLFxuICAgIF0sXG4gICAgQW55OiBbXG4gICAgICAgIFsxLCAwXSxcbiAgICAgICAgWzAsIDFdLFxuICAgIF0sXG4gICAgQWxsOiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDFdLFxuICAgIF0sXG59O1xuXG5leHBvcnQgeyBTY3JvbGxPZmZzZXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOnScrollHandler: function() { return /* binding */ createOnScrollHandler; }\n/* harmony export */ });\n/* harmony import */ var _utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/warn-once.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offsets/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\");\n\n\n\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (true) {\n        if (container && target && target !== container) {\n            (0,_utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: () => measure(element, options.target, info),\n        update: (time) => {\n            (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.updateScrollInfo)(element, info, time);\n            if (options.offset || options.target) {\n                (0,_offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffsets)(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrollInfo: function() { return /* binding */ scrollInfo; }\n/* harmony export */ });\n/* harmony import */ var _resize_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../resize/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./on-scroll-handler.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.documentElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.documentElement, ...options } = {}) {\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = (0,_info_mjs__WEBPACK_IMPORTED_MODULE_0__.createScrollInfo)();\n    const containerHandler = (0,_on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_1__.createOnScrollHandler)(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers)\n                handler.measure();\n        };\n        const updateAll = () => {\n            for (const handler of containerHandlers) {\n                handler.update(_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frameData.timestamp);\n            }\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        const listener = () => {\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(measureAll, false, true);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(updateAll, false, true);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.update(notifyAll, false, true);\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, (0,_resize_index_mjs__WEBPACK_IMPORTED_MODULE_3__.resize)(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n    }\n    const listener = scrollListeners.get(container);\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(listener, false, true);\n    return () => {\n        var _a;\n        (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: function() { return /* binding */ resolveElements; }\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n\n\nfunction resolveElements(elements, scope, selectorCache) {\n    var _a;\n    if (typeof elements === \"string\") {\n        let root = document;\n        if (scope) {\n            (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(Boolean(scope.current), \"Scope provided, but no element detected.\");\n            root = scope.current;\n        }\n        if (selectorCache) {\n            (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : (selectorCache[elements] = root.querySelectorAll(elements));\n            elements = selectorCache[elements];\n        }\n        else {\n            elements = root.querySelectorAll(elements);\n        }\n    }\n    else if (elements instanceof Element) {\n        elements = [elements];\n    }\n    /**\n     * Return an empty array\n     */\n    return Array.from(elements || []);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9yZXNvbHZlLWVsZW1lbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNEOztBQUV0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSw0REFBUztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9yZXNvbHZlLWVsZW1lbnQubWpzP2EzNGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW52YXJpYW50IH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvZXJyb3JzLm1qcyc7XG5cbmZ1bmN0aW9uIHJlc29sdmVFbGVtZW50cyhlbGVtZW50cywgc2NvcGUsIHNlbGVjdG9yQ2FjaGUpIHtcbiAgICB2YXIgX2E7XG4gICAgaWYgKHR5cGVvZiBlbGVtZW50cyA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBsZXQgcm9vdCA9IGRvY3VtZW50O1xuICAgICAgICBpZiAoc2NvcGUpIHtcbiAgICAgICAgICAgIGludmFyaWFudChCb29sZWFuKHNjb3BlLmN1cnJlbnQpLCBcIlNjb3BlIHByb3ZpZGVkLCBidXQgbm8gZWxlbWVudCBkZXRlY3RlZC5cIik7XG4gICAgICAgICAgICByb290ID0gc2NvcGUuY3VycmVudDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoc2VsZWN0b3JDYWNoZSkge1xuICAgICAgICAgICAgKF9hID0gc2VsZWN0b3JDYWNoZVtlbGVtZW50c10pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IChzZWxlY3RvckNhY2hlW2VsZW1lbnRzXSA9IHJvb3QucXVlcnlTZWxlY3RvckFsbChlbGVtZW50cykpO1xuICAgICAgICAgICAgZWxlbWVudHMgPSBzZWxlY3RvckNhY2hlW2VsZW1lbnRzXTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGVsZW1lbnRzID0gcm9vdC5xdWVyeVNlbGVjdG9yQWxsKGVsZW1lbnRzKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIGlmIChlbGVtZW50cyBpbnN0YW5jZW9mIEVsZW1lbnQpIHtcbiAgICAgICAgZWxlbWVudHMgPSBbZWxlbWVudHNdO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXR1cm4gYW4gZW1wdHkgYXJyYXlcbiAgICAgKi9cbiAgICByZXR1cm4gQXJyYXkuZnJvbShlbGVtZW50cyB8fCBbXSk7XG59XG5cbmV4cG9ydCB7IHJlc29sdmVFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/transform.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transform: function() { return /* binding */ transform; }\n/* harmony export */ });\n/* harmony import */ var _interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n\n\nconst isCustomValueType = (v) => {\n    return v && typeof v === \"object\" && v.mix;\n};\nconst getMixer = (v) => (isCustomValueType(v) ? v.mix : undefined);\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = (0,_interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__.interpolate)(inputRange, outputRange, {\n        mixer: getMixer(outputRange[0]),\n        ...options,\n    });\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5Qiw2REFBVztBQUNwQztBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRXFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcz8wY2EwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludGVycG9sYXRlIH0gZnJvbSAnLi9pbnRlcnBvbGF0ZS5tanMnO1xuXG5jb25zdCBpc0N1c3RvbVZhbHVlVHlwZSA9ICh2KSA9PiB7XG4gICAgcmV0dXJuIHYgJiYgdHlwZW9mIHYgPT09IFwib2JqZWN0XCIgJiYgdi5taXg7XG59O1xuY29uc3QgZ2V0TWl4ZXIgPSAodikgPT4gKGlzQ3VzdG9tVmFsdWVUeXBlKHYpID8gdi5taXggOiB1bmRlZmluZWQpO1xuZnVuY3Rpb24gdHJhbnNmb3JtKC4uLmFyZ3MpIHtcbiAgICBjb25zdCB1c2VJbW1lZGlhdGUgPSAhQXJyYXkuaXNBcnJheShhcmdzWzBdKTtcbiAgICBjb25zdCBhcmdPZmZzZXQgPSB1c2VJbW1lZGlhdGUgPyAwIDogLTE7XG4gICAgY29uc3QgaW5wdXRWYWx1ZSA9IGFyZ3NbMCArIGFyZ09mZnNldF07XG4gICAgY29uc3QgaW5wdXRSYW5nZSA9IGFyZ3NbMSArIGFyZ09mZnNldF07XG4gICAgY29uc3Qgb3V0cHV0UmFuZ2UgPSBhcmdzWzIgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IG9wdGlvbnMgPSBhcmdzWzMgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IGludGVycG9sYXRvciA9IGludGVycG9sYXRlKGlucHV0UmFuZ2UsIG91dHB1dFJhbmdlLCB7XG4gICAgICAgIG1peGVyOiBnZXRNaXhlcihvdXRwdXRSYW5nZVswXSksXG4gICAgICAgIC4uLm9wdGlvbnMsXG4gICAgfSk7XG4gICAgcmV0dXJuIHVzZUltbWVkaWF0ZSA/IGludGVycG9sYXRvcihpbnB1dFZhbHVlKSA6IGludGVycG9sYXRvcjtcbn1cblxuZXhwb3J0IHsgdHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-combine-values.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCombineMotionValues: function() { return /* binding */ useCombineMotionValues; }\n/* harmony export */ });\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.useMotionValue)(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        const scheduleUpdate = () => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.update(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(updateValue);\n        };\n    });\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-computed.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComputed: function() { return /* binding */ useComputed; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n\n\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    _index_mjs__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = [];\n    compute();\n    const value = (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__.useCombineMotionValues)(_index_mjs__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    _index_mjs__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = undefined;\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLWNvbXB1dGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0Q7QUFDZ0I7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJEQUFtQjtBQUN2QjtBQUNBLGtCQUFrQiwrRUFBc0IsQ0FBQywyREFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBbUI7QUFDdkI7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS91c2UtY29tcHV0ZWQubWpzPzcwNjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29sbGVjdE1vdGlvblZhbHVlcyB9IGZyb20gJy4vaW5kZXgubWpzJztcbmltcG9ydCB7IHVzZUNvbWJpbmVNb3Rpb25WYWx1ZXMgfSBmcm9tICcuL3VzZS1jb21iaW5lLXZhbHVlcy5tanMnO1xuXG5mdW5jdGlvbiB1c2VDb21wdXRlZChjb21wdXRlKSB7XG4gICAgLyoqXG4gICAgICogT3BlbiBzZXNzaW9uIG9mIGNvbGxlY3RNb3Rpb25WYWx1ZXMuIEFueSBNb3Rpb25WYWx1ZSB0aGF0IGNhbGxzIGdldCgpXG4gICAgICogd2lsbCBiZSBzYXZlZCBpbnRvIHRoaXMgYXJyYXkuXG4gICAgICovXG4gICAgY29sbGVjdE1vdGlvblZhbHVlcy5jdXJyZW50ID0gW107XG4gICAgY29tcHV0ZSgpO1xuICAgIGNvbnN0IHZhbHVlID0gdXNlQ29tYmluZU1vdGlvblZhbHVlcyhjb2xsZWN0TW90aW9uVmFsdWVzLmN1cnJlbnQsIGNvbXB1dGUpO1xuICAgIC8qKlxuICAgICAqIFN5bmNocm9ub3VzbHkgY2xvc2Ugc2Vzc2lvbiBvZiBjb2xsZWN0TW90aW9uVmFsdWVzLlxuICAgICAqL1xuICAgIGNvbGxlY3RNb3Rpb25WYWx1ZXMuY3VycmVudCA9IHVuZGVmaW5lZDtcbiAgICByZXR1cm4gdmFsdWU7XG59XG5cbmV4cG9ydCB7IHVzZUNvbXB1dGVkIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-motion-value.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValue: function() { return /* binding */ useMotionValue; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n\n\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(() => (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLW1vdGlvbi12YWx1ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBd0Q7QUFDZDtBQUMrQjtBQUNqQjs7QUFFeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLFNBQVM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixvRUFBVyxPQUFPLHVEQUFXO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFdBQVcsRUFBRSxpREFBVSxDQUFDLGlGQUFtQjtBQUN2RDtBQUNBLDhCQUE4QiwrQ0FBUTtBQUN0QyxRQUFRLGdEQUFTO0FBQ2pCO0FBQ0E7QUFDQTs7QUFFMEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS91c2UtbW90aW9uLXZhbHVlLm1qcz8zOGIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBtb3Rpb25WYWx1ZSB9IGZyb20gJy4vaW5kZXgubWpzJztcbmltcG9ydCB7IE1vdGlvbkNvbmZpZ0NvbnRleHQgfSBmcm9tICcuLi9jb250ZXh0L01vdGlvbkNvbmZpZ0NvbnRleHQubWpzJztcbmltcG9ydCB7IHVzZUNvbnN0YW50IH0gZnJvbSAnLi4vdXRpbHMvdXNlLWNvbnN0YW50Lm1qcyc7XG5cbi8qKlxuICogQ3JlYXRlcyBhIGBNb3Rpb25WYWx1ZWAgdG8gdHJhY2sgdGhlIHN0YXRlIGFuZCB2ZWxvY2l0eSBvZiBhIHZhbHVlLlxuICpcbiAqIFVzdWFsbHksIHRoZXNlIGFyZSBjcmVhdGVkIGF1dG9tYXRpY2FsbHkuIEZvciBhZHZhbmNlZCB1c2UtY2FzZXMsIGxpa2UgdXNlIHdpdGggYHVzZVRyYW5zZm9ybWAsIHlvdSBjYW4gY3JlYXRlIGBNb3Rpb25WYWx1ZWBzIGV4dGVybmFsbHkgYW5kIHBhc3MgdGhlbSBpbnRvIHRoZSBhbmltYXRlZCBjb21wb25lbnQgdmlhIHRoZSBgc3R5bGVgIHByb3AuXG4gKlxuICogYGBganN4XG4gKiBleHBvcnQgY29uc3QgTXlDb21wb25lbnQgPSAoKSA9PiB7XG4gKiAgIGNvbnN0IHNjYWxlID0gdXNlTW90aW9uVmFsdWUoMSlcbiAqXG4gKiAgIHJldHVybiA8bW90aW9uLmRpdiBzdHlsZT17eyBzY2FsZSB9fSAvPlxuICogfVxuICogYGBgXG4gKlxuICogQHBhcmFtIGluaXRpYWwgLSBUaGUgaW5pdGlhbCBzdGF0ZS5cbiAqXG4gKiBAcHVibGljXG4gKi9cbmZ1bmN0aW9uIHVzZU1vdGlvblZhbHVlKGluaXRpYWwpIHtcbiAgICBjb25zdCB2YWx1ZSA9IHVzZUNvbnN0YW50KCgpID0+IG1vdGlvblZhbHVlKGluaXRpYWwpKTtcbiAgICAvKipcbiAgICAgKiBJZiB0aGlzIG1vdGlvbiB2YWx1ZSBpcyBiZWluZyB1c2VkIGluIHN0YXRpYyBtb2RlLCBsaWtlIG9uXG4gICAgICogdGhlIEZyYW1lciBjYW52YXMsIGZvcmNlIGNvbXBvbmVudHMgdG8gcmVyZW5kZXIgd2hlbiB0aGUgbW90aW9uXG4gICAgICogdmFsdWUgaXMgdXBkYXRlZC5cbiAgICAgKi9cbiAgICBjb25zdCB7IGlzU3RhdGljIH0gPSB1c2VDb250ZXh0KE1vdGlvbkNvbmZpZ0NvbnRleHQpO1xuICAgIGlmIChpc1N0YXRpYykge1xuICAgICAgICBjb25zdCBbLCBzZXRMYXRlc3RdID0gdXNlU3RhdGUoaW5pdGlhbCk7XG4gICAgICAgIHVzZUVmZmVjdCgoKSA9PiB2YWx1ZS5vbihcImNoYW5nZVwiLCBzZXRMYXRlc3QpLCBbXSk7XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuZXhwb3J0IHsgdXNlTW90aW9uVmFsdWUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-scroll.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScroll: function() { return /* binding */ useScroll; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n/* harmony import */ var _render_dom_scroll_track_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../render/dom/scroll/track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\n\n\n\n\nfunction refWarning(name, ref) {\n    (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__.warning)(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollY: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollXProgress: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollYProgress: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsomorphicLayoutEffect\n        : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return (0,_render_dom_scroll_track_mjs__WEBPACK_IMPORTED_MODULE_5__.scrollInfo)(({ x, y }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n            target: (target === null || target === void 0 ? void 0 : target.current) || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-transform.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransform: function() { return /* binding */ useTransform; }\n/* harmony export */ });\n/* harmony import */ var _utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/transform.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/transform.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-computed.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\");\n\n\n\n\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return (0,_use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__.useComputed)(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : (0,_utils_transform_mjs__WEBPACK_IMPORTED_MODULE_1__.transform)(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(() => []);\n    return (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__.useCombineMotionValues)(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\n"));

/***/ })

});