"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/Hero */ \"(app-pages-browser)/./src/components/sections/Hero.tsx\");\n/* harmony import */ var _components_sections_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/About */ \"(app-pages-browser)/./src/components/sections/About.tsx\");\n/* harmony import */ var _components_sections_Services__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/Services */ \"(app-pages-browser)/./src/components/sections/Services.tsx\");\n/* harmony import */ var _components_sections_Portfolio__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/Portfolio */ \"(app-pages-browser)/./src/components/sections/Portfolio.tsx\");\n/* harmony import */ var _components_sections_Testimonials__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sections/Testimonials */ \"(app-pages-browser)/./src/components/sections/Testimonials.tsx\");\n/* harmony import */ var _components_sections_FAQ__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/sections/FAQ */ \"(app-pages-browser)/./src/components/sections/FAQ.tsx\");\n/* harmony import */ var _components_sections_Contact__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/sections/Contact */ \"(app-pages-browser)/./src/components/sections/Contact.tsx\");\n/* harmony import */ var _components_layout_Navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/layout/Navigation */ \"(app-pages-browser)/./src/components/layout/Navigation.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Register GSAP plugins\nif (true) {\n    gsap__WEBPACK_IMPORTED_MODULE_11__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_12__.ScrollTrigger);\n}\nfunction Home() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize GSAP animations\n        gsap__WEBPACK_IMPORTED_MODULE_11__.gsap.fromTo(\".fade-in-up\", {\n            y: 100,\n            opacity: 0\n        }, {\n            y: 0,\n            opacity: 1,\n            duration: 1,\n            stagger: 0.2,\n            scrollTrigger: {\n                trigger: \".fade-in-up\",\n                start: \"top 80%\",\n                end: \"bottom 20%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n        // Parallax effect for hero section\n        gsap__WEBPACK_IMPORTED_MODULE_11__.gsap.to(\".parallax-bg\", {\n            yPercent: -50,\n            ease: \"none\",\n            scrollTrigger: {\n                trigger: \".parallax-bg\",\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: true\n            }\n        });\n        // Text reveal animation\n        gsap__WEBPACK_IMPORTED_MODULE_11__.gsap.fromTo(\".text-reveal\", {\n            y: 100,\n            opacity: 0\n        }, {\n            y: 0,\n            opacity: 1,\n            duration: 1.2,\n            ease: \"power3.out\",\n            scrollTrigger: {\n                trigger: \".text-reveal\",\n                start: \"top 85%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n        return ()=>{\n            gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_12__.ScrollTrigger.getAll().forEach((trigger)=>trigger.kill());\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navigation__WEBPACK_IMPORTED_MODULE_9__.Navigation, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Hero__WEBPACK_IMPORTED_MODULE_2__.Hero, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_About__WEBPACK_IMPORTED_MODULE_3__.About, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Services__WEBPACK_IMPORTED_MODULE_4__.Services, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Portfolio__WEBPACK_IMPORTED_MODULE_5__.Portfolio, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Testimonials__WEBPACK_IMPORTED_MODULE_6__.Testimonials, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_FAQ__WEBPACK_IMPORTED_MODULE_7__.FAQ, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Contact__WEBPACK_IMPORTED_MODULE_8__.Contact, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_10__.Footer, {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/app/page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});