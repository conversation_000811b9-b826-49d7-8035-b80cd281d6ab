"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/Services.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/Services.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Services: function() { return /* binding */ Services; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ Services auto */ \n\nfunction Services() {\n    const services = [\n        {\n            icon: \"\\uD83C\\uDFA8\",\n            title: \"UI/UX დიზაინი\",\n            description: \"მომხმარებელზე ორიენტირებული, ინტუიტიური და ვიზუალურად მიმზიდველი დიზაინები\",\n            features: [\n                \"User Research\",\n                \"Wireframing\",\n                \"Prototyping\",\n                \"Visual Design\"\n            ],\n            color: \"from-pink-500 to-rose-500\"\n        },\n        {\n            icon: \"\\uD83D\\uDCBB\",\n            title: \"ვებ განვითარება\",\n            description: \"თანამედროვე, რესპონსიული და ოპტიმიზებული ვებსაიტები და ვებ აპლიკაციები\",\n            features: [\n                \"React/Next.js\",\n                \"Node.js\",\n                \"Database Design\",\n                \"API Development\"\n            ],\n            color: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            icon: \"\\uD83D\\uDCF1\",\n            title: \"მობილური აპები\",\n            description: \"iOS და Android პლატფორმებისთვის ნატიური და კროს-პლატფორმული აპლიკაციები\",\n            features: [\n                \"React Native\",\n                \"Flutter\",\n                \"Native Development\",\n                \"App Store Optimization\"\n            ],\n            color: \"from-green-500 to-emerald-500\"\n        },\n        {\n            icon: \"\\uD83D\\uDE80\",\n            title: \"SEO ოპტიმიზაცია\",\n            description: \"საძიებო სისტემებში თქვენი საიტის ხილვადობის გაზრდა და ორგანული ტრაფიკის მოზიდვა\",\n            features: [\n                \"Technical SEO\",\n                \"Content Strategy\",\n                \"Link Building\",\n                \"Analytics\"\n            ],\n            color: \"from-purple-500 to-violet-500\"\n        },\n        {\n            icon: \"\\uD83C\\uDFAE\",\n            title: \"თამაშების განვითარება\",\n            description: \"2D და 3D თამაშები ვებისთვის, მობილურისთვის და დესკტოპისთვის\",\n            features: [\n                \"Unity\",\n                \"Unreal Engine\",\n                \"Web Games\",\n                \"Mobile Games\"\n            ],\n            color: \"from-orange-500 to-red-500\"\n        },\n        {\n            icon: \"⚡\",\n            title: \"Performance ოპტიმიზაცია\",\n            description: \"საიტის სიჩქარის გაუმჯობესება და მომხმარებლის გამოცდილების ოპტიმიზაცია\",\n            features: [\n                \"Speed Optimization\",\n                \"Core Web Vitals\",\n                \"CDN Setup\",\n                \"Caching\"\n            ],\n            color: \"from-yellow-500 to-amber-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"services\",\n        className: \"py-20 lg:py-32 bg-gray-50 dark:bg-dark-800 relative overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\",\n                            children: [\n                                \"ჩვენი \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gradient\",\n                                    children: \"სერვისები\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                            children: \"ვთავაზობთ სრულ სპექტრს ციფრული სერვისებისა\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            whileHover: {\n                                y: -10,\n                                scale: 1.02\n                            },\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-dark-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-br \".concat(service.color, \" rounded-2xl flex items-center justify-center text-2xl mb-6\"),\n                                        children: service.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                                        children: service.title\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                        children: service.description\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: service.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center text-sm text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-gradient-to-r \".concat(service.color, \" rounded-full mr-3\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    feature\n                                                ]\n                                            }, featureIndex, true, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/webdesav/devoradev/src/components/sections/Services.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_c = Services;\nvar _c;\n$RefreshReg$(_c, \"Services\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Services.tsx\n"));

/***/ })

});