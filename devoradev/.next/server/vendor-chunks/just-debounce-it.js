"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/just-debounce-it";
exports.ids = ["vendor-chunks/just-debounce-it"];
exports.modules = {

/***/ "(ssr)/./node_modules/just-debounce-it/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/just-debounce-it/index.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ functionDebounce)\n/* harmony export */ });\nvar functionDebounce = debounce;\nfunction debounce(fn, wait, callFirst) {\n    var timeout = null;\n    var debouncedFn = null;\n    var clear = function() {\n        if (timeout) {\n            clearTimeout(timeout);\n            debouncedFn = null;\n            timeout = null;\n        }\n    };\n    var flush = function() {\n        var call = debouncedFn;\n        clear();\n        if (call) {\n            call();\n        }\n    };\n    var debounceWrapper = function() {\n        if (!wait) {\n            return fn.apply(this, arguments);\n        }\n        var context = this;\n        var args = arguments;\n        var callNow = callFirst && !timeout;\n        clear();\n        debouncedFn = function() {\n            fn.apply(context, args);\n        };\n        timeout = setTimeout(function() {\n            timeout = null;\n            if (!callNow) {\n                var call = debouncedFn;\n                debouncedFn = null;\n                return call();\n            }\n        }, wait);\n        if (callNow) {\n            return debouncedFn();\n        }\n    };\n    debounceWrapper.cancel = clear;\n    debounceWrapper.flush = flush;\n    return debounceWrapper;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/just-debounce-it/index.mjs\n");

/***/ })

};
;