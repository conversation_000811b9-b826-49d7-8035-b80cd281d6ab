// This file is for modularized imports for next/server to get fully-treeshaking.
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _unstablenostore.unstable_noStore;
    }
});
const _unstablenostore = require("../spec-extension/unstable-no-store");

//# sourceMappingURL=unstable-no-store.js.map