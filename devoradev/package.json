{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@studio-freight/react-lenis": "^0.0.46", "clsx": "^2.1.1", "framer-motion": "^10.16.16", "gsap": "^3.12.2", "next": "14.0.4", "next-themes": "^0.2.1", "react": "^18", "react-dom": "^18", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}