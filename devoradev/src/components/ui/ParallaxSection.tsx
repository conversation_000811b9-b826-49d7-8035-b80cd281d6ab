'use client'

import { useEffect, useRef } from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

interface ParallaxSectionProps {
  children: React.ReactNode
  speed?: number
  className?: string
  direction?: 'up' | 'down'
}

export function ParallaxSection({ 
  children, 
  speed = 0.5, 
  className = '',
  direction = 'up'
}: ParallaxSectionProps) {
  const ref = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start']
  })

  const y = useTransform(
    scrollYProgress,
    [0, 1],
    direction === 'up' ? ['0%', '-50%'] : ['0%', '50%']
  )

  return (
    <motion.div
      ref={ref}
      style={{ y }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

interface ScrollRevealProps {
  children: React.ReactNode
  className?: string
  delay?: number
  duration?: number
  distance?: number
  direction?: 'up' | 'down' | 'left' | 'right'
}

export function ScrollReveal({
  children,
  className = '',
  delay = 0,
  duration = 0.8,
  distance = 50,
  direction = 'up'
}: ScrollRevealProps) {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger)

      const element = ref.current
      if (!element) return

      const directionMap = {
        up: { y: distance, x: 0 },
        down: { y: -distance, x: 0 },
        left: { x: distance, y: 0 },
        right: { x: -distance, y: 0 },
      }

      gsap.fromTo(
        element,
        {
          ...directionMap[direction],
          opacity: 0,
        },
        {
          x: 0,
          y: 0,
          opacity: 1,
          duration,
          delay,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: element,
            start: 'top 85%',
            toggleActions: 'play none none reverse',
          },
        }
      )
    }

    // Cleanup function
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [delay, duration, distance, direction])

  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  )
}

interface CounterAnimationProps {
  from: number
  to: number
  duration?: number
  suffix?: string
  className?: string
}

export function CounterAnimation({
  from,
  to,
  duration = 2,
  suffix = '',
  className = ''
}: CounterAnimationProps) {
  const ref = useRef<HTMLSpanElement>(null)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger)

      const element = ref.current
      if (!element) return

      const obj = { value: from }
      gsap.to(obj, {
        value: to,
        duration,
        ease: 'power2.out',
        onUpdate: function() {
          if (element) {
            element.textContent = Math.round(obj.value) + suffix
          }
        },
        scrollTrigger: {
          trigger: element,
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        },
      })
    }

    // Cleanup function
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [from, to, duration, suffix])

  return <span ref={ref} className={className}>{from}{suffix}</span>
}
