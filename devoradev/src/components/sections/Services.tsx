'use client'

import { motion } from 'framer-motion'

export function Services() {
  const services = [
    {
      icon: '🎨',
      title: 'UI/UX დიზაინი',
      description: 'მომხმარებელზე ორიენტირებული, ინტუიტიური და ვიზუალურად მიმზიდველი დიზაინები',
      features: ['User Research', 'Wireframing', 'Prototyping', 'Visual Design'],
      color: 'from-pink-500 to-rose-500',
    },
    {
      icon: '💻',
      title: 'ვებ განვითარება',
      description: 'თანამედროვე, რესპონსიული და ოპტიმიზებული ვებსაიტები და ვებ აპლიკაციები',
      features: ['React/Next.js', 'Node.js', 'Database Design', 'API Development'],
      color: 'from-blue-500 to-cyan-500',
    },
    {
      icon: '📱',
      title: 'მობილური აპები',
      description: 'iOS და Android პლატფორმებისთვის ნატიური და კროს-პლატფორმული აპლიკაციები',
      features: ['React Native', 'Flutter', 'Native Development', 'App Store Optimization'],
      color: 'from-green-500 to-emerald-500',
    },
    {
      icon: '🚀',
      title: 'SEO ოპტიმიზაცია',
      description: 'საძიებო სისტემებში თქვენი საიტის ხილვადობის გაზრდა და ორგანული ტრაფიკის მოზიდვა',
      features: ['Technical SEO', 'Content Strategy', 'Link Building', 'Analytics'],
      color: 'from-purple-500 to-violet-500',
    },
    {
      icon: '🎮',
      title: 'თამაშების განვითარება',
      description: '2D და 3D თამაშები ვებისთვის, მობილურისთვის და დესკტოპისთვის',
      features: ['Unity', 'Unreal Engine', 'Web Games', 'Mobile Games'],
      color: 'from-orange-500 to-red-500',
    },
    {
      icon: '⚡',
      title: 'Performance ოპტიმიზაცია',
      description: 'საიტის სიჩქარის გაუმჯობესება და მომხმარებლის გამოცდილების ოპტიმიზაცია',
      features: ['Speed Optimization', 'Core Web Vitals', 'CDN Setup', 'Caching'],
      color: 'from-yellow-500 to-amber-500',
    },
  ]

  return (
    <section
      id="services"
      className="py-20 lg:py-32 bg-gray-50 dark:bg-dark-800 relative overflow-hidden"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            ჩვენი <span className="text-gradient">სერვისები</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            ვთავაზობთ სრულ სპექტრს ციფრული სერვისებისა
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="group"
            >
              <div className="bg-white dark:bg-dark-900 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 h-full">
                <div className={`w-16 h-16 bg-gradient-to-br ${service.color} rounded-2xl flex items-center justify-center text-2xl mb-6`}>
                  {service.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {service.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <div className={`w-2 h-2 bg-gradient-to-r ${service.color} rounded-full mr-3`}></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
