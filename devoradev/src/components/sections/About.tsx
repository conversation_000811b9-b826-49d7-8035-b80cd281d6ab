'use client'

import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { CounterAnimation } from '@/components/ui/ParallaxSection'
import { FadeInText } from '@/components/ui/AnimatedText'

export function About() {
  const sectionRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger)

      // Text reveal animation
      gsap.fromTo(
        '.about-text',
        { y: 50, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          stagger: 0.2,
          scrollTrigger: {
            trigger: sectionRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse',
          },
        }
      )

      // Stats counter animation
      gsap.fromTo(
        '.stat-number',
        { innerText: 0 },
        {
          innerText: (i, target) => target.getAttribute('data-value'),
          duration: 2,
          ease: 'power2.out',
          snap: { innerText: 1 },
          scrollTrigger: {
            trigger: '.stats-container',
            start: 'top 80%',
            toggleActions: 'play none none reverse',
          },
        }
      )
    }
  }, [])

  const stats = [
    { number: '50', label: 'დასრულებული პროექტი', suffix: '+' },
    { number: '25', label: 'კმაყოფილი კლიენტი', suffix: '+' },
    { number: '3', label: 'წლიანი გამოცდილება', suffix: '+' },
    { number: '24', label: 'საათიანი მხარდაჭერა', suffix: '/7' },
  ]

  return (
    <section
      ref={sectionRef}
      id="about"
      className="py-20 lg:py-32 bg-white dark:bg-dark-900 relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="about-text"
            >
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                ჩვენი <span className="text-gradient">ისტორია</span>
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 leading-relaxed mb-6">
                Devora Dev არის თანამედროვე ვებ სტუდია, რომელიც 2021 წელს დაარსდა 
                ციფრული ტექნოლოგიების მიმართ ვნებით. ჩვენი მისიაა ბიზნესებისთვის 
                უნიკალური და ეფექტური ციფრული გადაწყვეტების შექმნა.
              </p>
              <p className="text-lg text-gray-600 dark:text-gray-400 leading-relaxed">
                ჩვენ ვთანამშრომლობთ როგორც მცირე სტარტაპებთან, ასევე დიდ კორპორაციებთან, 
                ყოველთვის ვცდილობთ გადავაჭარბოთ მოლოდინებს და შევქმნათ პროდუქტები, 
                რომლებიც ნამდვილად ცვლის ბიზნესის მიმართულებას.
              </p>
            </motion.div>

            {/* Values */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="about-text space-y-4"
            >
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                ჩვენი ღირებულებები
              </h3>
              <div className="space-y-3">
                {[
                  { icon: '🎯', title: 'ინოვაცია', desc: 'ყოველთვის ვიყენებთ უახლეს ტექნოლოგიებს' },
                  { icon: '🤝', title: 'პარტნიორობა', desc: 'კლიენტებთან გრძელვადიანი ურთიერთობა' },
                  { icon: '⚡', title: 'ხარისხი', desc: 'უმაღლესი ხარისხის სტანდარტები' },
                ].map((value, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <span className="text-2xl">{value.icon}</span>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">{value.title}</h4>
                      <p className="text-gray-600 dark:text-gray-400">{value.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Right Content - Stats */}
          <div className="stats-container">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              className="bg-gradient-to-br from-primary-50 to-purple-50 dark:from-primary-900/20 dark:to-purple-900/20 rounded-3xl p-8 lg:p-12"
            >
              <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
                ჩვენი მიღწევები
              </h3>
              
              <div className="grid grid-cols-2 gap-8">
                {stats.map((stat, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="text-center"
                  >
                    <div className="text-3xl lg:text-4xl font-bold text-primary-500 mb-2">
                      <CounterAnimation
                        from={0}
                        to={parseInt(stat.number)}
                        suffix={stat.suffix}
                        duration={2}
                      />
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm lg:text-base">
                      {stat.label}
                    </p>
                  </motion.div>
                ))}
              </div>

              {/* Team Preview */}
              <div className="mt-12 text-center">
                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                  ჩვენი გუნდი
                </h4>
                <div className="flex justify-center space-x-4">
                  {[1, 2, 3, 4].map((_, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ scale: 1.1 }}
                      className="w-12 h-12 bg-gradient-to-br from-primary-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold"
                    >
                      {String.fromCharCode(65 + index)}
                    </motion.div>
                  ))}
                </div>
                <p className="text-gray-600 dark:text-gray-400 mt-4">
                  გამოცდილი პროფესიონალების გუნდი
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
