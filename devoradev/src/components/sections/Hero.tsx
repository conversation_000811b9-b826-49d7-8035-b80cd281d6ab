'use client'

import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { AnimatedText, TypewriterText } from '@/components/ui/AnimatedText'
import { ParallaxSection } from '@/components/ui/ParallaxSection'

export function Hero() {
  const heroRef = useRef<HTMLDivElement>(null)
  const textRef = useRef<HTMLDivElement>(null)
  const backgroundRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger)

      // Hero text animation
      const tl = gsap.timeline()
      tl.fromTo(
        '.hero-title',
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' }
      )
      .fromTo(
        '.hero-subtitle',
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: 'power3.out' },
        '-=0.8'
      )
      .fromTo(
        '.hero-description',
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: 'power3.out' },
        '-=0.6'
      )
      .fromTo(
        '.hero-cta',
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: 'power3.out' },
        '-=0.4'
      )

      // Parallax background
      gsap.to(backgroundRef.current, {
        yPercent: -50,
        ease: 'none',
        scrollTrigger: {
          trigger: heroRef.current,
          start: 'top bottom',
          end: 'bottom top',
          scrub: true,
        },
      })

      // Floating animation for decorative elements
      gsap.to('.floating-element', {
        y: -20,
        duration: 2,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.3,
      })
    }
  }, [])

  return (
    <section
      ref={heroRef}
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900"
    >
      {/* Animated Background */}
      <div
        ref={backgroundRef}
        className="absolute inset-0 parallax-bg"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-purple-500/10 dark:from-primary-400/5 dark:to-purple-400/5"></div>
        
        {/* Floating geometric shapes */}
        <div className="floating-element absolute top-20 left-10 w-20 h-20 bg-primary-500/20 rounded-full blur-xl"></div>
        <div className="floating-element absolute top-40 right-20 w-32 h-32 bg-purple-500/20 rounded-full blur-xl"></div>
        <div className="floating-element absolute bottom-40 left-20 w-24 h-24 bg-blue-500/20 rounded-full blur-xl"></div>
        <div className="floating-element absolute bottom-20 right-10 w-16 h-16 bg-pink-500/20 rounded-full blur-xl"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div ref={textRef} className="space-y-8">
          {/* Main Title */}
          <div className="hero-title text-5xl md:text-7xl lg:text-8xl font-bold leading-tight">
            <AnimatedText
              text="თანამედროვე"
              className="text-gradient block"
              delay={0.2}
              stagger={0.08}
            />
            <AnimatedText
              text="ვებ სტუდია"
              className="text-gray-900 dark:text-white block"
              delay={0.8}
              stagger={0.08}
            />
          </div>

          {/* Subtitle */}
          <div className="hero-subtitle text-xl md:text-2xl lg:text-3xl font-medium text-gray-700 dark:text-gray-300 max-w-4xl mx-auto">
            <TypewriterText
              text="ჩვენ ვქმნით დაუვიწყარ ციფრულ გამოცდილებებს"
              delay={1.5}
              speed={80}
            />
          </div>

          {/* Description */}
          <motion.p 
            className="hero-description text-lg md:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            SEO ოპტიმიზაციიდან UI/UX დიზაინამდე, ვებ აპლიკაციებიდან მობილურ აპებამდე - 
            ჩვენ გთავაზობთ სრულ სპექტრს ციფრული სერვისებისა, რომლებიც თქვენს ბიზნესს 
            წარმატებისკენ მიგიყვანთ.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div 
            className="hero-cta flex flex-col sm:flex-row gap-4 justify-center items-center pt-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 text-lg"
            >
              პროექტის დაწყება
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 bg-transparent border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:border-primary-500 hover:text-primary-500 font-semibold rounded-full transition-all duration-300 text-lg"
            >
              ჩვენი სამუშაოები
            </motion.button>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <div className="w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center">
              <div className="w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2 animate-pulse"></div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Decorative Grid */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
    </section>
  )
}
