'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ScrollReveal } from '@/components/ui/ParallaxSection'

export function FAQ() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(0)

  const faqs = [
    {
      question: 'რამდენ ხანში მზადდება ვებსაიტი?',
      answer: 'ვებსაიტის განვითარების ვადები დამოკიდებულია პროექტის სირთულეზე. მარტივი ვებსაიტი 2-3 კვირაში, რთული ვებ აპლიკაცია კი 2-3 თვეში მზადდება. ყოველი პროექტისთვის ინდივიდუალურად განვსაზღვრავთ ვადებს.',
    },
    {
      question: 'რა ღირს ვებსაიტის შექმნა?',
      answer: 'ფასი დამოკიდებულია პროექტის მოთხოვნებზე. მარტივი ვებსაიტი იწყება 1500 ლარიდან, ხოლო რთული ვებ აპლიკაციები 5000 ლარიდან. ზუსტი ფასის გასაცნობად დაგვიკავშირდით უფასო კონსულტაციისთვის.',
    },
    {
      question: 'გაქვთ თუ არა მხარდაჭერის სერვისი?',
      answer: 'დიახ, ჩვენ ვთავაზობთ 24/7 ტექნიკურ მხარდაჭერას. პროექტის დასრულების შემდეგ 3 თვის განმავლობაში უფასო მხარდაჭერა ვრცელდება, შემდეგ კი შეგიძლიათ შეიძინოთ მხარდაჭერის პაკეტი.',
    },
    {
      question: 'შეგიძლიათ არსებული ვებსაიტის გაუმჯობესება?',
      answer: 'რა თქმა უნდა! ჩვენ ვთავაზობთ არსებული ვებსაიტების რედიზაინს, ოპტიმიზაციას და ახალი ფუნქციების დამატებას. პირველ რიგში ვაკეთებთ ანალიზს და შემდეგ ვთავაზობთ გაუმჯობესების გეგმას.',
    },
    {
      question: 'რა ტექნოლოგიებს იყენებთ?',
      answer: 'ჩვენ ვიყენებთ უახლეს ტექნოლოგიებს: React, Next.js, Node.js, TypeScript, Python, PHP/Laravel, MongoDB, PostgreSQL და სხვა. ყოველი პროექტისთვის ვირჩევთ ყველაზე შესაფერის ტექნოლოგიურ სტეკს.',
    },
    {
      question: 'გაქვთ SEO ოპტიმიზაციის სერვისი?',
      answer: 'დიახ, ჩვენ ვთავაზობთ სრულ SEO სერვისს: ტექნიკური SEO, კონტენტ ოპტიმიზაცია, კეთილშობილი ლინკების შექმნა, ანალიტიკა და რეგულარული რეპორტები. ჩვენი მიზანია თქვენი საიტის ხილვადობის მაქსიმალური გაზრდა.',
    },
    {
      question: 'შეგიძლიათ მობილური აპლიკაციის შექმნა?',
      answer: 'დიახ, ჩვენ ვქმნით როგორც ნატიურ (iOS/Android), ასევე კროს-პლატფორმულ მობილურ აპლიკაციებს React Native და Flutter-ის გამოყენებით. ასევე ვთავაზობთ App Store-ში და Google Play-ში გამოქვეყნების სერვისს.',
    },
    {
      question: 'რა არის თქვენი სამუშაო პროცესი?',
      answer: 'ჩვენი პროცესი მოიცავს 5 ეტაპს: 1) კონსულტაცია და მოთხოვნების ანალიზი, 2) დიზაინის შექმნა და დამტკიცება, 3) განვითარება და ტესტირება, 4) გაშვება და ოპტიმიზაცია, 5) მხარდაჭერა და მონიტორინგი.',
    },
  ]

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index)
  }

  return (
    <section className="py-20 lg:py-32 bg-white dark:bg-dark-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-40 right-20 w-64 h-64 bg-primary-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-40 left-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <ScrollReveal className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            ხშირად დასმული <span className="text-gradient">კითხვები</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400">
            იპოვეთ პასუხები ყველაზე ხშირად დასმულ კითხვებზე
          </p>
        </ScrollReveal>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <ScrollReveal
              key={index}
              delay={index * 0.1}
            >
              <motion.div
                className="bg-gray-50 dark:bg-dark-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
                whileHover={{ scale: 1.01 }}
              >
                <motion.button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-6 py-6 text-left flex items-center justify-between focus:outline-none"
                  whileTap={{ scale: 0.99 }}
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{ rotate: openFAQ === index ? 45 : 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </motion.div>
                </motion.button>

                <AnimatePresence>
                  {openFAQ === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6">
                        <div className="h-px bg-gray-200 dark:bg-gray-700 mb-4"></div>
                        <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            </ScrollReveal>
          ))}
        </div>

        {/* Contact CTA */}
        <ScrollReveal className="text-center mt-16">
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
            ვერ იპოვეთ პასუხი თქვენს კითხვაზე?
          </p>
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-4 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
          >
            დაგვიკავშირდით
          </motion.button>
        </ScrollReveal>
      </div>
    </section>
  )
}
