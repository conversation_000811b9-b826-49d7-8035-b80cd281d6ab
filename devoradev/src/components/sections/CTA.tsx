'use client'

import { motion } from 'framer-motion'
import { FloatingElement } from '@/components/ui/InteractiveCard'

export function CTA() {
  return (
    <section className="py-20 lg:py-32 bg-gradient-to-br from-primary-600 via-purple-600 to-pink-600 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute top-40 right-20 w-48 h-48 bg-white/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-40 right-10 w-36 h-36 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Floating Elements */}
      <FloatingElement className="absolute top-20 left-20" amplitude={15} duration={4}>
        <div className="w-8 h-8 bg-white/20 rounded-lg rotate-45"></div>
      </FloatingElement>
      
      <FloatingElement className="absolute top-32 right-32" amplitude={20} duration={3} delay={1}>
        <div className="w-6 h-6 bg-white/30 rounded-full"></div>
      </FloatingElement>
      
      <FloatingElement className="absolute bottom-32 left-32" amplitude={18} duration={5} delay={2}>
        <div className="w-10 h-10 bg-white/15 rounded-full"></div>
      </FloatingElement>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Main Heading */}
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            მზად ხართ თქვენი{' '}
            <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
              ციფრული
            </span>{' '}
            მომავლის შესაქმნელად?
          </h2>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl lg:text-2xl text-white/90 mb-8 leading-relaxed"
          >
            ჩვენ ვქმნით ვებსაიტებს, აპლიკაციებს და ციფრულ გამოცდილებებს, 
            რომლებიც თქვენს ბიზნესს ახალ დონეზე აიყვანს
          </motion.p>

          {/* Features List */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="grid md:grid-cols-3 gap-6 mb-12"
          >
            {[
              { icon: '⚡', text: 'სწრაფი განვითარება' },
              { icon: '🎯', text: 'ინდივიდუალური მიდგომა' },
              { icon: '🚀', text: 'გარანტირებული შედეგი' },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center justify-center space-x-3 text-white"
              >
                <span className="text-2xl">{feature.icon}</span>
                <span className="font-medium">{feature.text}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 bg-white text-primary-600 font-bold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 text-lg"
            >
              უფასო კონსულტაცია
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 bg-transparent border-2 border-white text-white font-bold rounded-full hover:bg-white hover:text-primary-600 transition-all duration-300 text-lg"
            >
              ჩვენი სამუშაოები
            </motion.button>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1 }}
            viewport={{ once: true }}
            className="mt-12 text-white/80"
          >
            <p className="mb-4">ან დაგვიკავშირდით პირდაპირ:</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center text-sm">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-2 hover:text-white transition-colors"
              >
                <span>📧</span>
                <span><EMAIL></span>
              </a>
              <a
                href="tel:+995555123456"
                className="flex items-center space-x-2 hover:text-white transition-colors"
              >
                <span>📱</span>
                <span>+995 555 123 456</span>
              </a>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Decorative Grid */}
      <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
    </section>
  )
}
