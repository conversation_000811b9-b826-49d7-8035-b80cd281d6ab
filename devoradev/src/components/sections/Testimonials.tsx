'use client'

import { useEffect, useRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { ScrollReveal } from '@/components/ui/ParallaxSection'

export function Testimonials() {
  const sectionRef = useRef<HTMLDivElement>(null)
  const [activeTestimonial, setActiveTestimonial] = useState(0)

  const testimonials = [
    {
      id: 1,
      name: 'ნინო გელაშვილი',
      position: 'CEO, TechStart',
      company: 'TechStart',
      image: '/api/placeholder/80/80',
      rating: 5,
      text: 'Devora Dev-მა ჩვენი კომპანიის ვებსაიტი სრულიად გარდაქმნა. მათი პროფესიონალიზმი და ყურადღება დეტალებზე უბრალოდ შთამბეჭდავია. ახლა ჩვენი ონლაინ გაყიდვები 300%-ით გაიზარდა!',
      project: 'E-commerce Platform',
    },
    {
      id: 2,
      name: 'გიორგი მამაცაშვილი',
      position: 'მარკეტინგის დირექტორი',
      company: 'Digital Agency',
      image: '/api/placeholder/80/80',
      rating: 5,
      text: 'ჩვენი მობილური აპლიკაციის განვითარება Devora Dev-თან იყო ნამდვილი სიამოვნება. მათმა გუნდმა ყველა ჩვენი მოლოდინი გადააჭარბა და შედეგი უბრალოდ შესანიშნავია.',
      project: 'Mobile Banking App',
    },
    {
      id: 3,
      name: 'ანა ხარაიშვილი',
      position: 'ფაუნდერი',
      company: 'Creative Studio',
      image: '/api/placeholder/80/80',
      rating: 5,
      text: 'SEO ოპტიმიზაციის შემდეგ ჩვენი საიტი Google-ის პირველ გვერდზე აღმოჩნდა. ორგანული ტრაფიკი 500%-ით გაიზარდა. Devora Dev-ის გუნდი ნამდვილად იცის საქმე!',
      project: 'SEO Optimization',
    },
    {
      id: 4,
      name: 'დავით ლომიძე',
      position: 'CTO',
      company: 'FinTech Solutions',
      image: '/api/placeholder/80/80',
      rating: 5,
      text: 'ტექნიკური კომპლექსურობის მიუხედავად, Devora Dev-მა ჩვენი ფინტექ პლატფორმა დროულად და ბიუჯეტის ფარგლებში შექმნა. მათი ექსპერტიზა უბრალოდ შეუდარებელია.',
      project: 'FinTech Platform',
    },
  ]

  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger)

      // Auto-rotate testimonials
      const interval = setInterval(() => {
        setActiveTestimonial((prev) => (prev + 1) % testimonials.length)
      }, 5000)

      return () => clearInterval(interval)
    }
  }, [testimonials.length])

  return (
    <section
      ref={sectionRef}
      className="py-20 lg:py-32 bg-gradient-to-br from-primary-50 to-purple-50 dark:from-primary-900/10 dark:to-purple-900/10 relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <ScrollReveal className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            რას ამბობენ ჩვენი <span className="text-gradient">კლიენტები</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            ჩვენი კლიენტების წარმატება არის ჩვენი მთავარი მიზანი
          </p>
        </ScrollReveal>

        {/* Main Testimonial */}
        <div className="max-w-4xl mx-auto mb-16">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTestimonial}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ duration: 0.5 }}
              className="bg-white dark:bg-dark-900 rounded-3xl p-8 lg:p-12 shadow-2xl"
            >
              {/* Stars */}
              <div className="flex justify-center mb-6">
                {[...Array(testimonials[activeTestimonial].rating)].map((_, i) => (
                  <motion.span
                    key={i}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: i * 0.1 }}
                    className="text-yellow-400 text-2xl"
                  >
                    ⭐
                  </motion.span>
                ))}
              </div>

              {/* Quote */}
              <blockquote className="text-xl lg:text-2xl text-gray-700 dark:text-gray-300 text-center mb-8 leading-relaxed">
                "{testimonials[activeTestimonial].text}"
              </blockquote>

              {/* Author */}
              <div className="flex items-center justify-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {testimonials[activeTestimonial].name.charAt(0)}
                </div>
                <div className="text-center">
                  <h4 className="font-semibold text-gray-900 dark:text-white text-lg">
                    {testimonials[activeTestimonial].name}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    {testimonials[activeTestimonial].position}
                  </p>
                  <p className="text-primary-500 font-medium">
                    {testimonials[activeTestimonial].company}
                  </p>
                </div>
              </div>

              {/* Project Tag */}
              <div className="text-center mt-6">
                <span className="px-4 py-2 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium">
                  პროექტი: {testimonials[activeTestimonial].project}
                </span>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Testimonial Navigation */}
        <div className="flex justify-center space-x-4 mb-16">
          {testimonials.map((_, index) => (
            <motion.button
              key={index}
              onClick={() => setActiveTestimonial(index)}
              className={`w-4 h-4 rounded-full transition-all duration-300 ${
                index === activeTestimonial
                  ? 'bg-primary-500 scale-125'
                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
              }`}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
            />
          ))}
        </div>

        {/* All Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {testimonials.map((testimonial, index) => (
            <ScrollReveal
              key={testimonial.id}
              delay={index * 0.1}
              className="cursor-pointer"
              onClick={() => setActiveTestimonial(index)}
            >
              <motion.div
                whileHover={{ scale: 1.05, y: -5 }}
                className={`bg-white dark:bg-dark-900 rounded-2xl p-6 shadow-lg transition-all duration-300 ${
                  index === activeTestimonial
                    ? 'ring-2 ring-primary-500 shadow-xl'
                    : 'hover:shadow-xl'
                }`}
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                    {testimonial.name.charAt(0)}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {testimonial.company}
                    </p>
                  </div>
                </div>
                <p className="text-gray-700 dark:text-gray-300 text-sm line-clamp-3">
                  {testimonial.text}
                </p>
              </motion.div>
            </ScrollReveal>
          ))}
        </div>
      </div>
    </section>
  )
}
