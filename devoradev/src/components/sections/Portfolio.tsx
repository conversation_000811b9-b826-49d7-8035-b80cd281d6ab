'use client'

import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

export function Portfolio() {
  const sectionRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger)

      const portfolioItems = document.querySelectorAll('.portfolio-item')
      if (portfolioItems.length > 0 && sectionRef.current) {
        gsap.fromTo(
          portfolioItems,
          { y: 100, opacity: 0 },
          {
            y: 0,
            opacity: 1,
            duration: 1,
            stagger: 0.2,
            scrollTrigger: {
              trigger: sectionRef.current,
              start: 'top 80%',
              toggleActions: 'play none none reverse',
            },
          }
        )
      }
    }

    // Cleanup function
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const projects = [
    {
      title: 'E-commerce Platform',
      category: 'ვებ განვითარება',
      description: 'თანამედროვე ონლაინ მაღაზია React და Node.js-ით',
      image: '/api/placeholder/600/400',
      tags: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      color: 'from-blue-500 to-purple-600',
    },
    {
      title: 'Mobile Banking App',
      category: 'მობილური აპი',
      description: 'უსაფრთხო და მომხმარებელზე ორიენტირებული ბანკის აპი',
      image: '/api/placeholder/600/400',
      tags: ['React Native', 'Firebase', 'Biometric Auth'],
      color: 'from-green-500 to-teal-600',
    },
    {
      title: 'Restaurant Website',
      category: 'UI/UX დიზაინი',
      description: 'ელეგანტური რესტორნის ვებსაიტი ონლაინ შეკვეთის სისტემით',
      image: '/api/placeholder/600/400',
      tags: ['Figma', 'Next.js', 'Tailwind', 'Animation'],
      color: 'from-orange-500 to-red-600',
    },
    {
      title: 'Puzzle Game',
      category: 'თამაშის განვითარება',
      description: '3D puzzle თამაში Unity-ით მობილური პლატფორმებისთვის',
      image: '/api/placeholder/600/400',
      tags: ['Unity', 'C#', '3D Graphics', 'Mobile'],
      color: 'from-purple-500 to-pink-600',
    },
  ]

  return (
    <section
      ref={sectionRef}
      id="portfolio"
      className="py-20 lg:py-32 bg-white dark:bg-dark-900 relative overflow-hidden"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            ჩვენი <span className="text-gradient">სამუშაოები</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            აღმოაჩინეთ ჩვენი უახლესი პროექტები და იხილეთ, როგორ ვეხმარებით 
            კლიენტებს მათი ციფრული მიზნების მიღწევაში
          </p>
        </motion.div>

        {/* Portfolio Grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.02 }}
              className="portfolio-item group cursor-pointer"
            >
              <div className="bg-gray-50 dark:bg-dark-800 rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500">
                {/* Project Image */}
                <div className="relative h-64 overflow-hidden">
                  <div className={`absolute inset-0 bg-gradient-to-br ${project.color} opacity-20`}></div>
                  <div className="absolute inset-0 bg-gray-300 dark:bg-gray-700 flex items-center justify-center">
                    <span className="text-6xl opacity-50">🖼️</span>
                  </div>
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                    <motion.div
                      initial={{ scale: 0, opacity: 0 }}
                      whileHover={{ scale: 1, opacity: 1 }}
                      className="bg-white/90 dark:bg-dark-900/90 rounded-full p-4"
                    >
                      <span className="text-2xl">👁️</span>
                    </motion.div>
                  </div>
                </div>

                {/* Project Info */}
                <div className="p-8">
                  <div className="flex items-center justify-between mb-4">
                    <span className={`px-3 py-1 bg-gradient-to-r ${project.color} text-white text-sm font-medium rounded-full`}>
                      {project.category}
                    </span>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="text-gray-400 hover:text-primary-500 transition-colors"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </motion.button>
                  </div>

                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-primary-500 transition-colors">
                    {project.title}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                    {project.description}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2">
                    {project.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All Projects CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-16"
        >
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-4 bg-gradient-to-r from-primary-500 to-purple-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
          >
            ყველა პროექტის ნახვა
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}
