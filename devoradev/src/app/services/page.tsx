'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { Navigation } from '@/components/layout/Navigation'
import { Footer } from '@/components/layout/Footer'
import { ScrollReveal } from '@/components/ui/ParallaxSection'
import { InteractiveCard } from '@/components/ui/InteractiveCard'

export default function ServicesPage() {
  const services = [
    {
      id: 'web-development',
      icon: '💻',
      title: 'ვებ განვითარება',
      shortDesc: 'თანამედროვე, რესპონსიული და ოპტიმიზებული ვებსაიტები',
      fullDesc: 'ჩვენ ვქმნით მაღალი ხარისხის ვებსაიტებს და ვებ აპლიკაციებს, რომლებიც არა მხოლოდ ლამაზად გამოიყურება, არამედ სწრაფად მუშაობს და მომხმარებლისთვის მოსახერხებელია. ვიყენებთ უახლეს ტექნოლოგიებს როგორიცაა React, Next.js, Node.js და სხვა.',
      features: [
        'რესპონსიული დიზაინი',
        'SEO ოპტიმიზაცია',
        'სწრაფი ჩატვირთვა',
        'უსაფრთხოება',
        'CMS ინტეგრაცია',
        'E-commerce ფუნქციონალი'
      ],
      technologies: ['React', 'Next.js', 'Node.js', 'TypeScript', 'MongoDB', 'PostgreSQL'],
      price: 'დაწყება 2000₾-დან',
      duration: '2-6 კვირა',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      id: 'ui-ux-design',
      icon: '🎨',
      title: 'UI/UX დიზაინი',
      shortDesc: 'მომხმარებელზე ორიენტირებული, ინტუიტიური დიზაინები',
      fullDesc: 'ჩვენი დიზაინერების გუნდი ქმნის ვიზუალურად მიმზიდველ და ფუნქციონალურ დიზაინებს. ყოველი პროექტი იწყება მომხმარებლის კვლევით და მთავრდება დეტალური პროტოტიპით.',
      features: [
        'User Research',
        'Wireframing',
        'Visual Design',
        'Prototyping',
        'Usability Testing',
        'Design System'
      ],
      technologies: ['Figma', 'Adobe XD', 'Sketch', 'InVision', 'Principle', 'Framer'],
      price: 'დაწყება 1500₾-დან',
      duration: '1-4 კვირა',
      color: 'from-pink-500 to-rose-500',
    },
    {
      id: 'mobile-development',
      icon: '📱',
      title: 'მობილური აპები',
      shortDesc: 'iOS და Android აპლიკაციები',
      fullDesc: 'ვქმნით ნატიურ და კროს-პლატფორმულ მობილურ აპლიკაციებს, რომლებიც იდეალურად მუშაობს ყველა მოწყობილობაზე. ჩვენი აპები გამოირჩევა მაღალი წარმადობით და მომხმარებლისთვის მოსახერხებელი ინტერფეისით.',
      features: [
        'Native Development',
        'Cross-platform',
        'App Store Optimization',
        'Push Notifications',
        'Offline Functionality',
        'Analytics Integration'
      ],
      technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Firebase', 'AWS'],
      price: 'დაწყება 3000₾-დან',
      duration: '3-8 კვირა',
      color: 'from-green-500 to-emerald-500',
    },
    {
      id: 'seo-optimization',
      icon: '🚀',
      title: 'SEO ოპტიმიზაცია',
      shortDesc: 'საძიებო სისტემებში ხილვადობის გაზრდა',
      fullDesc: 'ჩვენი SEO ექსპერტები დაგეხმარებათ თქვენი ვებსაიტის რანკინგის გაუმჯობესებაში Google-ში და სხვა საძიებო სისტემებში. ვიყენებთ თეთრი ქუდის SEO ტექნიკებს გრძელვადიანი შედეგებისთვის.',
      features: [
        'Technical SEO Audit',
        'Keyword Research',
        'Content Optimization',
        'Link Building',
        'Local SEO',
        'Analytics & Reporting'
      ],
      technologies: ['Google Analytics', 'Search Console', 'SEMrush', 'Ahrefs', 'Screaming Frog'],
      price: 'დაწყება 800₾-დან',
      duration: '2-6 თვე',
      color: 'from-purple-500 to-violet-500',
    },
    {
      id: 'game-development',
      icon: '🎮',
      title: 'თამაშების განვითარება',
      shortDesc: '2D და 3D თამაშები ყველა პლატფორმისთვის',
      fullDesc: 'ჩვენი თამაშების განვითარების გუნდი ქმნის საინტერესო და ადიქტურ თამაშებს ვებისთვის, მობილურისთვის და დესკტოპისთვის. ვიყენებთ Unity და Unreal Engine-ს მაღალი ხარისხის თამაშების შესაქმნელად.',
      features: [
        '2D/3D Graphics',
        'Game Mechanics',
        'Multiplayer Support',
        'In-App Purchases',
        'Leaderboards',
        'Cross-platform'
      ],
      technologies: ['Unity', 'Unreal Engine', 'C#', 'JavaScript', 'Blender', 'Photoshop'],
      price: 'დაწყება 5000₾-დან',
      duration: '2-6 თვე',
      color: 'from-orange-500 to-red-500',
    },
    {
      id: 'performance-optimization',
      icon: '⚡',
      title: 'Performance ოპტიმიზაცია',
      shortDesc: 'საიტის სიჩქარის გაუმჯობესება',
      fullDesc: 'ვაუმჯობესებთ თქვენი ვებსაიტის წარმადობას, რაც გულისხმობს სწრაფ ჩატვირთვას, უკეთეს მომხმარებლის გამოცდილებას და მაღალ SEO რანკინგს. ჩვენი ოპტიმიზაცია მოიცავს კოდის, სურათების და სერვერის ოპტიმიზაციას.',
      features: [
        'Speed Optimization',
        'Core Web Vitals',
        'Image Optimization',
        'CDN Setup',
        'Caching Strategy',
        'Database Optimization'
      ],
      technologies: ['Lighthouse', 'GTMetrix', 'CloudFlare', 'AWS', 'Redis', 'Nginx'],
      price: 'დაწყება 1200₾-დან',
      duration: '1-3 კვირა',
      color: 'from-yellow-500 to-amber-500',
    },
  ]

  return (
    <>
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              ჩვენი <span className="text-gradient">სერვისები</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              სრული სპექტრი ციფრული სერვისებისა თქვენი ბიზნესის წარმატებისთვის
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-gray-50 dark:bg-dark-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12">
            {services.map((service, index) => (
              <ScrollReveal
                key={service.id}
                delay={index * 0.1}
              >
                <InteractiveCard className="h-full">
                  <div className="bg-white dark:bg-dark-900 rounded-3xl p-8 shadow-lg h-full">
                    {/* Service Header */}
                    <div className="flex items-center space-x-4 mb-6">
                      <div className={`w-16 h-16 bg-gradient-to-br ${service.color} rounded-2xl flex items-center justify-center text-2xl`}>
                        {service.icon}
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                          {service.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">
                          {service.shortDesc}
                        </p>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                      {service.fullDesc}
                    </p>

                    {/* Features */}
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                        რას მოიცავს:
                      </h4>
                      <div className="grid grid-cols-2 gap-2">
                        {service.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <div className={`w-2 h-2 bg-gradient-to-r ${service.color} rounded-full mr-2`}></div>
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Technologies */}
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                        ტექნოლოგიები:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {service.technologies.map((tech, techIndex) => (
                          <span
                            key={techIndex}
                            className="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-sm rounded-full"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Price & Duration */}
                    <div className="flex justify-between items-center mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-xl">
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">ფასი</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{service.price}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">ვადა</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{service.duration}</p>
                      </div>
                    </div>

                    {/* CTA */}
                    <motion.button
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      className={`w-full px-6 py-3 bg-gradient-to-r ${service.color} text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300`}
                    >
                      პროექტის განხილვა
                    </motion.button>
                  </div>
                </InteractiveCard>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white dark:bg-dark-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <ScrollReveal>
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              მზად ხართ პროექტის დასაწყებად?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
              დაგვიკავშირდით უფასო კონსულტაციისთვის და მიიღეთ ინდივიდუალური შეთავაზება
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
              >
                უფასო კონსულტაცია
              </motion.button>
              <Link href="/portfolio">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 bg-transparent border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:border-primary-500 hover:text-primary-500 font-semibold rounded-full transition-all duration-300"
                >
                  ჩვენი სამუშაოები
                </motion.button>
              </Link>
            </div>
          </ScrollReveal>
        </div>
      </section>

      <Footer />
    </>
  )
}
