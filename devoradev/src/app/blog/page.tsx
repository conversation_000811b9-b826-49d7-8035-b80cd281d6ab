'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { Navigation } from '@/components/layout/Navigation'
import { Footer } from '@/components/layout/Footer'

export default function BlogPage() {
  const blogPosts = [
    {
      id: 1,
      title: 'React 18-ის ახალი ფუნქციები',
      excerpt: 'გაეცანით React 18-ის ყველაზე მნიშვნელოვან ახალ ფუნქციას და იხილეთ როგორ გამოიყენოთ ისინი თქვენს პროექტებში.',
      date: '2024-01-15',
      category: 'React',
      readTime: '5 წუთი',
      image: '/api/placeholder/400/250',
    },
    {
      id: 2,
      title: 'Next.js 14 - რა არის ახალი?',
      excerpt: 'Next.js 14-ის ყველაზე საინტერესო ახალი ფუნქციები და გაუმჯობესებები, რომლებიც თქვენს განვითარების პროცესს გაუადვილებს.',
      date: '2024-01-10',
      category: 'Next.js',
      readTime: '7 წუთი',
      image: '/api/placeholder/400/250',
    },
    {
      id: 3,
      title: 'Tailwind CSS-ის საუკეთესო პრაქტიკები',
      excerpt: 'ისწავლეთ Tailwind CSS-ის ეფექტური გამოყენება და შექმენით მშვენიერი, რესპონსიული დიზაინები.',
      date: '2024-01-05',
      category: 'CSS',
      readTime: '6 წუთი',
      image: '/api/placeholder/400/250',
    },
    {
      id: 4,
      title: 'TypeScript-ის Advanced ტიპები',
      excerpt: 'ღრმად შეისწავლეთ TypeScript-ის რთული ტიპები და გაიუმჯობესეთ კოდის ხარისხი.',
      date: '2023-12-28',
      category: 'TypeScript',
      readTime: '8 წუთი',
      image: '/api/placeholder/400/250',
    },
    {
      id: 5,
      title: 'GSAP ანიმაციების შექმნა',
      excerpt: 'ისწავლეთ GSAP-ით მშვენიერი ანიმაციების შექმნა და თქვენი ვებსაიტის ინტერაქტიულობის გაზრდა.',
      date: '2023-12-20',
      category: 'Animation',
      readTime: '10 წუთი',
      image: '/api/placeholder/400/250',
    },
    {
      id: 6,
      title: 'SEO ოპტიმიზაციის სახელმძღვანელო',
      excerpt: 'სრული სახელმძღვანელო SEO ოპტიმიზაციისთვის და საძიებო სისტემებში რანკინგის გაუმჯობესებისთვის.',
      date: '2023-12-15',
      category: 'SEO',
      readTime: '12 წუთი',
      image: '/api/placeholder/400/250',
    },
  ]

  const categories = ['ყველა', 'React', 'Next.js', 'CSS', 'TypeScript', 'Animation', 'SEO']

  return (
    <>
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              ჩვენი <span className="text-gradient">ბლოგი</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              უახლესი სიახლეები ვებ განვითარების სამყაროდან, ტუტორიალები და საუკეთესო პრაქტიკები
            </p>
          </motion.div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-8 bg-white dark:bg-dark-900 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category, index) => (
              <motion.button
                key={category}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                  index === 0
                    ? 'bg-primary-500 text-white shadow-lg'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                {category}
              </motion.button>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-20 bg-gray-50 dark:bg-dark-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -10 }}
                className="bg-white dark:bg-dark-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300"
              >
                {/* Post Image */}
                <div className="relative h-48 bg-gradient-to-br from-primary-500 to-purple-600 flex items-center justify-center">
                  <span className="text-6xl opacity-50">📝</span>
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 bg-white/90 text-primary-600 text-sm font-medium rounded-full">
                      {post.category}
                    </span>
                  </div>
                </div>

                {/* Post Content */}
                <div className="p-6">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                    <span>{post.date}</span>
                    <span className="mx-2">•</span>
                    <span>{post.readTime}</span>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 hover:text-primary-500 transition-colors">
                    <Link href={`/blog/${post.id}`}>
                      {post.title}
                    </Link>
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">
                    {post.excerpt}
                  </p>
                  
                  <Link
                    href={`/blog/${post.id}`}
                    className="inline-flex items-center text-primary-500 hover:text-primary-600 font-medium transition-colors"
                  >
                    ვრცლად
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </motion.article>
            ))}
          </div>

          {/* Load More Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="text-center mt-16"
          >
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
            >
              მეტი სტატიის ნახვა
            </motion.button>
          </motion.div>
        </div>
      </section>

      <Footer />
    </>
  )
}
