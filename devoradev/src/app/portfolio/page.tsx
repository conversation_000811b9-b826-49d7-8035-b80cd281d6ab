'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { Navigation } from '@/components/layout/Navigation'
import { Footer } from '@/components/layout/Footer'

export default function PortfolioPage() {
  const [activeFilter, setActiveFilter] = useState('ყველა')

  const categories = ['ყველა', 'ვებ განვითარება', 'მობილური აპი', 'UI/UX დიზაინი', 'E-commerce', 'SEO']

  const projects = [
    {
      id: 1,
      title: 'TechStart E-commerce Platform',
      category: 'E-commerce',
      description: 'სრული ფუნქციონალის ონლაინ მაღაზია თანამედროვე დიზაინით და მოწინავე ფუნქციებით',
      image: '/api/placeholder/600/400',
      tags: ['React', 'Next.js', 'Node.js', 'MongoDB', 'Stripe'],
      client: 'TechStart LLC',
      year: '2024',
      duration: '3 თვე',
      url: 'https://techstart.ge',
      features: [
        'რესპონსიული დიზაინი',
        'გადახდის სისტემა',
        'ინვენტარის მართვა',
        'მომხმარებლის პანელი',
        'ადმინ პანელი',
        'SEO ოპტიმიზაცია'
      ],
      results: [
        '300% გაყიდვების ზრდა',
        '50% მომხმარებლების ზრდა',
        '40% უკეთესი კონვერსია'
      ]
    },
    {
      id: 2,
      title: 'BankApp Mobile Application',
      category: 'მობილური აპი',
      description: 'უსაფრთხო მობილური ბანკის აპლიკაცია ბიომეტრიული ავთენტიფიკაციით',
      image: '/api/placeholder/600/400',
      tags: ['React Native', 'Firebase', 'Biometric Auth', 'Redux'],
      client: 'Georgian Bank',
      year: '2024',
      duration: '4 თვე',
      url: 'https://app.georgianbank.ge',
      features: [
        'ბიომეტრიული ავთენტიფიკაცია',
        'ტრანზაქციების ისტორია',
        'QR კოდით გადახდა',
        'ბალანსის შემოწმება',
        'Push ნოტიფიკაციები',
        'ოფლაინ რეჟიმი'
      ],
      results: [
        '500K+ ჩამოტვირთვა',
        '4.8/5 რეიტინგი',
        '90% მომხმარებლის შენარჩუნება'
      ]
    },
    {
      id: 3,
      title: 'Restaurant Booking System',
      category: 'ვებ განვითარება',
      description: 'რესტორნის ონლაინ ჯავშნის სისტემა რეალურ დროში',
      image: '/api/placeholder/600/400',
      tags: ['Vue.js', 'Laravel', 'MySQL', 'Socket.io'],
      client: 'Fine Dining Group',
      year: '2023',
      duration: '2 თვე',
      url: 'https://finedining.ge',
      features: [
        'რეალურ დროში ჯავშნა',
        'მაგიდების ვიზუალიზაცია',
        'მენიუს ინტეგრაცია',
        'SMS ნოტიფიკაციები',
        'ადმინ პანელი',
        'ანალიტიკა'
      ],
      results: [
        '200% ჯავშნების ზრდა',
        '30% ოპერაციული ხარჯების შემცირება',
        '95% კლიენტების კმაყოფილება'
      ]
    },
    {
      id: 4,
      title: 'Corporate Website Redesign',
      category: 'UI/UX დიზაინი',
      description: 'კორპორაციული ვებსაიტის სრული რედიზაინი თანამედროვე მიდგომით',
      image: '/api/placeholder/600/400',
      tags: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
      client: 'Global Corp',
      year: '2023',
      duration: '6 კვირა',
      url: 'https://globalcorp.ge',
      features: [
        'User Research',
        'Wireframing',
        'Visual Design',
        'Prototyping',
        'Usability Testing',
        'Design System'
      ],
      results: [
        '60% უკეთესი UX მეტრიკები',
        '45% მეტი ტრაფიკი',
        '80% მომხმარებლების კმაყოფილება'
      ]
    },
    {
      id: 5,
      title: 'Educational Platform',
      category: 'ვებ განვითარება',
      description: 'ონლაინ სწავლების პლატფორმა ვიდეო კურსებით და ტესტებით',
      image: '/api/placeholder/600/400',
      tags: ['React', 'Django', 'PostgreSQL', 'AWS'],
      client: 'EduTech Georgia',
      year: '2023',
      duration: '5 თვე',
      url: 'https://edutech.ge',
      features: [
        'ვიდეო სტრიმინგი',
        'ინტერაქტიული ტესტები',
        'პროგრესის ტრეკინგი',
        'სერტიფიკატები',
        'ფორუმი',
        'მობილური აპი'
      ],
      results: [
        '10K+ რეგისტრირებული მომხმარებელი',
        '85% კურსის დასრულების მაჩვენებელი',
        '4.7/5 კლიენტების შეფასება'
      ]
    },
    {
      id: 6,
      title: 'SEO Campaign Success',
      category: 'SEO',
      description: 'კომპლექსური SEO კამპანია ლოკალური ბიზნესისთვის',
      image: '/api/placeholder/600/400',
      tags: ['Technical SEO', 'Content Strategy', 'Link Building', 'Analytics'],
      client: 'Local Business Network',
      year: '2023',
      duration: '6 თვე',
      url: 'https://localbusiness.ge',
      features: [
        'Technical SEO Audit',
        'Keyword Research',
        'Content Optimization',
        'Local SEO',
        'Link Building',
        'Performance Monitoring'
      ],
      results: [
        '400% ორგანული ტრაფიკის ზრდა',
        'Top 3 პოზიცია 50+ კეყვორდზე',
        '250% ლიდების ზრდა'
      ]
    },
  ]

  const filteredProjects = activeFilter === 'ყველა' 
    ? projects 
    : projects.filter(project => project.category === activeFilter)

  return (
    <>
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              ჩვენი <span className="text-gradient">პორტფოლიო</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              აღმოაჩინეთ ჩვენი უახლესი პროექტები და იხილეთ, როგორ ვეხმარებით 
              კლიენტებს მათი ციფრული მიზნების მიღწევაში
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filter Tabs */}
      <section className="py-8 bg-white dark:bg-dark-900 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category, index) => (
              <motion.button
                key={category}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setActiveFilter(category)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  activeFilter === category
                    ? 'bg-primary-500 text-white shadow-lg'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                {category}
              </motion.button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20 bg-gray-50 dark:bg-dark-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeFilter}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -10 }}
                  className="bg-white dark:bg-dark-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300"
                >
                  {/* Project Image */}
                  <div className="relative h-48 bg-gradient-to-br from-primary-500 to-purple-600 flex items-center justify-center">
                    <span className="text-6xl opacity-50">🖼️</span>
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 bg-white/90 text-primary-600 text-sm font-medium rounded-full">
                        {project.category}
                      </span>
                    </div>
                    <div className="absolute top-4 right-4">
                      <span className="px-3 py-1 bg-black/50 text-white text-sm rounded-full">
                        {project.year}
                      </span>
                    </div>
                  </div>

                  {/* Project Info */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      {project.title}
                    </h3>
                    
                    <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                      {project.description}
                    </p>

                    {/* Client & Duration */}
                    <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                      <span>კლიენტი: {project.client}</span>
                      <span>ვადა: {project.duration}</span>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.tags.slice(0, 3).map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                      {project.tags.length > 3 && (
                        <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs rounded-full">
                          +{project.tags.length - 3}
                        </span>
                      )}
                    </div>

                    {/* CTA */}
                    <div className="flex gap-2">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="flex-1 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white text-sm font-medium rounded-lg transition-colors"
                      >
                        დეტალები
                      </motion.button>
                      <motion.a
                        href={project.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-4 py-2 bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-lg transition-colors"
                      >
                        ნახვა
                      </motion.a>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </AnimatePresence>
        </div>
      </section>

      <Footer />
    </>
  )
}
